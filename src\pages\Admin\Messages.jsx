import React, { useState } from 'react'
import { 
  Search, 
  Filter, 
  Eye,
  Reply,
  Trash2,
  Mail,
  MailOpen,
  Clock,
  User,
  Phone,
  MapPin,
  Star,
  Archive
} from 'lucide-react'
import { Button, Input, Card, Badge, Modal, Alert, Textarea } from '../../components/UI'
import { PageHeader } from '../../components/Common'

const AdminMessages = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedMessage, setSelectedMessage] = useState(null)
  const [showMessageModal, setShowMessageModal] = useState(false)
  const [showReplyModal, setShowReplyModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [replyText, setReplyText] = useState('')

  // بيانات وهمية للرسائل
  const [messagesList, setMessagesList] = useState([
    {
      id: 1,
      name: 'أحمد محمد علي',
      email: '<EMAIL>',
      phone: '+964 ************',
      location: 'الكرادة، بغداد',
      subject: 'استفسار حول مشروع تطوير البنية التحتية',
      message: 'السلام عليكم ورحمة الله وبركاته، أود الاستفسار عن موعد بدء مشروع تطوير البنية التحتية في منطقة الكرادة وما هي المراحل المخطط لها؟',
      date: '2025-01-15T10:30:00',
      status: 'unread',
      priority: 'high',
      category: 'مشاريع'
    },
    {
      id: 2,
      name: 'فاطمة حسن',
      email: '<EMAIL>',
      phone: '+964 ************',
      location: 'الجادرية، بغداد',
      subject: 'شكر وتقدير',
      message: 'أتقدم بالشكر والتقدير لجهودكم المبذولة في خدمة المواطنين، وخاصة في مشروع المدرسة الجديدة في حي الجادرية.',
      date: '2025-01-14T14:20:00',
      status: 'read',
      priority: 'normal',
      category: 'شكر',
      reply: 'شكراً لكم على كلماتكم الطيبة، نحن نعمل دائماً لخدمة مواطنينا الكرام.'
    },
    {
      id: 3,
      name: 'محمد عبد الله',
      email: '<EMAIL>',
      phone: '+964 ************',
      location: 'الكندي، بغداد',
      subject: 'طلب مساعدة في قضية شخصية',
      message: 'أحتاج إلى مساعدة في حل مشكلة تتعلق بالحصول على الخدمات الصحية في مستشفى الكندي.',
      date: '2025-01-13T09:15:00',
      status: 'replied',
      priority: 'urgent',
      category: 'خدمات'
    }
  ])

  const statusOptions = [
    { value: 'all', label: 'جميع الرسائل' },
    { value: 'unread', label: 'غير مقروءة', color: 'primary', icon: Mail },
    { value: 'read', label: 'مقروءة', color: 'secondary', icon: MailOpen },
    { value: 'replied', label: 'تم الرد', color: 'success', icon: Reply },
    { value: 'archived', label: 'مؤرشفة', color: 'warning', icon: Archive }
  ]

  const priorityOptions = [
    { value: 'urgent', label: 'عاجل', color: 'danger' },
    { value: 'high', label: 'مهم', color: 'warning' },
    { value: 'normal', label: 'عادي', color: 'secondary' }
  ]

  const categories = [
    'مشاريع',
    'خدمات',
    'شكاوى',
    'اقتراحات',
    'شكر',
    'استفسارات',
    'أخرى'
  ]

  // تصفية الرسائل
  const filteredMessages = messagesList.filter(message => {
    const matchesSearch = message.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.message.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || message.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const handleViewMessage = (message) => {
    setSelectedMessage(message)
    setShowMessageModal(true)
    
    // تحديث حالة الرسالة إلى مقروءة
    if (message.status === 'unread') {
      setMessagesList(prev => 
        prev.map(m => 
          m.id === message.id ? { ...m, status: 'read' } : m
        )
      )
    }
  }

  const handleReplyMessage = (message) => {
    setSelectedMessage(message)
    setReplyText('')
    setShowReplyModal(true)
  }

  const handleDeleteMessage = (message) => {
    setSelectedMessage(message)
    setShowDeleteModal(true)
  }

  const confirmDelete = () => {
    setMessagesList(prev => prev.filter(message => message.id !== selectedMessage.id))
    setShowDeleteModal(false)
    setSelectedMessage(null)
  }

  const sendReply = () => {
    if (replyText.trim()) {
      setMessagesList(prev => 
        prev.map(m => 
          m.id === selectedMessage.id 
            ? { ...m, status: 'replied', reply: replyText }
            : m
        )
      )
      setShowReplyModal(false)
      setReplyText('')
      setSelectedMessage(null)
    }
  }

  const getStatusBadge = (status) => {
    const statusConfig = statusOptions.find(s => s.value === status)
    if (!statusConfig) return null
    
    const Icon = statusConfig.icon
    return (
      <Badge variant={statusConfig.color} className="flex items-center space-x-1 space-x-reverse">
        <Icon className="w-3 h-3" />
        <span>{statusConfig.label}</span>
      </Badge>
    )
  }

  const getPriorityBadge = (priority) => {
    const priorityConfig = priorityOptions.find(p => p.value === priority)
    if (!priorityConfig) return null
    
    return (
      <Badge variant={priorityConfig.color} size="sm">
        {priorityConfig.label}
      </Badge>
    )
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'منذ أقل من ساعة'
    } else if (diffInHours < 24) {
      return `منذ ${diffInHours} ساعة`
    } else {
      return date.toLocaleDateString('ar-EG')
    }
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <PageHeader
        title="إدارة الرسائل"
        description="متابعة والرد على رسائل المواطنين"
      />

      {/* أدوات البحث والتصفية */}
      <Card>
        <Card.Content className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="البحث في الرسائل..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>

            {/* تصفية الحالة */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
            >
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>

            {/* إحصائيات سريعة */}
            <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
              <span>المجموع: {filteredMessages.length}</span>
              <span>جديدة: {filteredMessages.filter(m => m.status === 'unread').length}</span>
              <span>تم الرد: {filteredMessages.filter(m => m.status === 'replied').length}</span>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* قائمة الرسائل */}
      <div className="space-y-4">
        {filteredMessages.length === 0 ? (
          <Card>
            <Card.Content className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                لا توجد رسائل
              </h3>
              <p className="text-gray-600">
                لم يتم العثور على رسائل تطابق معايير البحث
              </p>
            </Card.Content>
          </Card>
        ) : (
          filteredMessages.map(message => (
            <Card 
              key={message.id} 
              className={`hover:shadow-lg transition-shadow cursor-pointer ${
                message.status === 'unread' ? 'border-r-4 border-r-iraqi-blue bg-blue-50/30' : ''
              }`}
              onClick={() => handleViewMessage(message)}
            >
              <Card.Content className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* رأس الرسالة */}
                    <div className="flex items-center space-x-3 space-x-reverse mb-2">
                      <h3 className={`text-lg font-medium ${
                        message.status === 'unread' ? 'text-gray-900 font-semibold' : 'text-gray-700'
                      }`}>
                        {message.name}
                      </h3>
                      {getStatusBadge(message.status)}
                      {getPriorityBadge(message.priority)}
                    </div>

                    {/* موضوع الرسالة */}
                    <h4 className={`text-base mb-2 ${
                      message.status === 'unread' ? 'font-semibold text-gray-900' : 'text-gray-800'
                    }`}>
                      {message.subject}
                    </h4>

                    {/* نص الرسالة */}
                    <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                      {message.message}
                    </p>

                    {/* معلومات إضافية */}
                    <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Clock className="w-4 h-4" />
                        <span>{formatDate(message.date)}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Phone className="w-4 h-4" />
                        <span>{message.phone}</span>
                      </div>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <MapPin className="w-4 h-4" />
                        <span>{message.location}</span>
                      </div>
                      <Badge variant="outline" size="sm">
                        {message.category}
                      </Badge>
                    </div>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleViewMessage(message)
                      }}
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleReplyMessage(message)
                      }}
                    >
                      <Reply className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteMessage(message)
                      }}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </Card.Content>
            </Card>
          ))
        )}
      </div>

      {/* مودال عرض الرسالة */}
      <Modal 
        isOpen={showMessageModal} 
        onClose={() => setShowMessageModal(false)}
        title="تفاصيل الرسالة"
        size="lg"
      >
        {selectedMessage && (
          <div className="space-y-6">
            {/* معلومات المرسل */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">الاسم</label>
                  <p className="text-gray-900">{selectedMessage.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">البريد الإلكتروني</label>
                  <p className="text-gray-900">{selectedMessage.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">رقم الهاتف</label>
                  <p className="text-gray-900">{selectedMessage.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">الموقع</label>
                  <p className="text-gray-900">{selectedMessage.location}</p>
                </div>
              </div>
            </div>

            {/* موضوع الرسالة */}
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-2">الموضوع</label>
              <p className="text-lg font-semibold text-gray-900">{selectedMessage.subject}</p>
            </div>

            {/* نص الرسالة */}
            <div>
              <label className="text-sm font-medium text-gray-700 block mb-2">الرسالة</label>
              <div className="bg-white border rounded-lg p-4">
                <p className="text-gray-900 leading-relaxed whitespace-pre-wrap">
                  {selectedMessage.message}
                </p>
              </div>
            </div>

            {/* الرد إذا كان موجوداً */}
            {selectedMessage.reply && (
              <div>
                <label className="text-sm font-medium text-gray-700 block mb-2">الرد المرسل</label>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <p className="text-gray-900 leading-relaxed">
                    {selectedMessage.reply}
                  </p>
                </div>
              </div>
            )}

            {/* أزرار الإجراءات */}
            <div className="flex justify-end space-x-3 space-x-reverse">
              <Button 
                variant="ghost" 
                onClick={() => setShowMessageModal(false)}
              >
                إغلاق
              </Button>
              <Button 
                variant="primary" 
                onClick={() => {
                  setShowMessageModal(false)
                  handleReplyMessage(selectedMessage)
                }}
              >
                <Reply className="w-4 h-4" />
                <span className="mr-2">رد على الرسالة</span>
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* مودال الرد */}
      <Modal 
        isOpen={showReplyModal} 
        onClose={() => setShowReplyModal(false)}
        title="الرد على الرسالة"
      >
        {selectedMessage && (
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">
                رد على: {selectedMessage.name}
              </h4>
              <p className="text-sm text-gray-600">
                الموضوع: {selectedMessage.subject}
              </p>
            </div>

            <Textarea
              label="نص الرد"
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              placeholder="اكتب ردك هنا..."
              rows={6}
              required
            />

            <div className="flex justify-end space-x-3 space-x-reverse">
              <Button 
                variant="ghost" 
                onClick={() => setShowReplyModal(false)}
              >
                إلغاء
              </Button>
              <Button 
                variant="primary" 
                onClick={sendReply}
                disabled={!replyText.trim()}
              >
                إرسال الرد
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* مودال تأكيد الحذف */}
      <Modal 
        isOpen={showDeleteModal} 
        onClose={() => setShowDeleteModal(false)}
        title="تأكيد الحذف"
      >
        <div className="space-y-4">
          <Alert variant="warning" title="تحذير">
            هل أنت متأكد من حذف هذه الرسالة؟ لا يمكن التراجع عن هذا الإجراء.
          </Alert>
          
          {selectedMessage && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">
                من: {selectedMessage.name}
              </h4>
              <p className="text-sm text-gray-600 mb-2">
                الموضوع: {selectedMessage.subject}
              </p>
              <p className="text-sm text-gray-500">
                التاريخ: {formatDate(selectedMessage.date)}
              </p>
            </div>
          )}

          <div className="flex justify-end space-x-3 space-x-reverse">
            <Button 
              variant="ghost" 
              onClick={() => setShowDeleteModal(false)}
            >
              إلغاء
            </Button>
            <Button 
              variant="danger" 
              onClick={confirmDelete}
            >
              حذف الرسالة
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default AdminMessages
