import React from 'react'

const Switch = ({ 
  checked = false, 
  onChange, 
  disabled = false, 
  size = 'md',
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-4',
    md: 'w-11 h-6',
    lg: 'w-14 h-8'
  }

  const thumbSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-5 h-5', 
    lg: 'w-6 h-6'
  }

  const translateClasses = {
    sm: checked ? 'translate-x-4' : 'translate-x-0',
    md: checked ? 'translate-x-5' : 'translate-x-0',
    lg: checked ? 'translate-x-6' : 'translate-x-0'
  }

  return (
    <button
      type="button"
      className={`
        relative inline-flex flex-shrink-0 border-2 border-transparent rounded-full cursor-pointer 
        transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-iraqi-blue
        ${sizeClasses[size]}
        ${checked ? 'bg-iraqi-blue' : 'bg-gray-200'}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      role="switch"
      aria-checked={checked}
      onClick={() => !disabled && onChange && onChange(!checked)}
      disabled={disabled}
    >
      <span
        aria-hidden="true"
        className={`
          pointer-events-none inline-block rounded-full bg-white shadow transform ring-0 
          transition ease-in-out duration-200
          ${thumbSizeClasses[size]}
          ${translateClasses[size]}
        `}
      />
    </button>
  )
}

export default Switch
