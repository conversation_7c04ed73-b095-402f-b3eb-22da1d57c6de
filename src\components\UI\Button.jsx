import React from 'react'
import { Loader2 } from 'lucide-react'

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon: Icon,
  iconPosition = 'left',
  className = '',
  onClick,
  type = 'button',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variants = {
    primary: 'bg-iraqi-blue text-white hover:bg-blue-700 focus:ring-iraqi-blue shadow-md hover:shadow-lg',
    secondary: 'bg-iraqi-gold text-white hover:bg-yellow-600 focus:ring-iraqi-gold shadow-md hover:shadow-lg',
    success: 'bg-iraqi-green text-white hover:bg-green-700 focus:ring-iraqi-green shadow-md hover:shadow-lg',
    danger: 'bg-iraqi-red text-white hover:bg-red-700 focus:ring-iraqi-red shadow-md hover:shadow-lg',
    outline: 'border-2 border-iraqi-blue text-iraqi-blue hover:bg-iraqi-blue hover:text-white focus:ring-iraqi-blue',
    ghost: 'text-iraqi-blue hover:bg-blue-50 focus:ring-iraqi-blue',
    link: 'text-iraqi-blue hover:text-blue-700 underline-offset-4 hover:underline focus:ring-iraqi-blue'
  }
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl'
  }
  
  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-7 h-7'
  }
  
  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`
  
  const renderIcon = () => {
    if (loading) {
      return <Loader2 className={`${iconSizes[size]} animate-spin`} />
    }
    
    if (Icon) {
      return <Icon className={iconSizes[size]} />
    }
    
    return null
  }
  
  const iconElement = renderIcon()
  
  return (
    <button
      type={type}
      className={classes}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {iconElement && iconPosition === 'left' && (
        <span className={children ? 'ml-2' : ''}>
          {iconElement}
        </span>
      )}
      
      {children}
      
      {iconElement && iconPosition === 'right' && (
        <span className={children ? 'mr-2' : ''}>
          {iconElement}
        </span>
      )}
    </button>
  )
}

// مكونات فرعية للاستخدام السريع
export const PrimaryButton = (props) => <Button variant="primary" {...props} />
export const SecondaryButton = (props) => <Button variant="secondary" {...props} />
export const OutlineButton = (props) => <Button variant="outline" {...props} />
export const GhostButton = (props) => <Button variant="ghost" {...props} />
export const LinkButton = (props) => <Button variant="link" {...props} />

export default Button
