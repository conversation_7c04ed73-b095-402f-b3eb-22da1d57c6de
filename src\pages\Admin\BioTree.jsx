import React, { useState } from 'react'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Eye,
  Link,
  ExternalLink,
  Move,
  Save,
  Copy,
  Share2,
  BarChart3,
  Globe,
  Instagram,
  Twitter,
  Facebook,
  Youtube,
  Linkedin
} from 'lucide-react'
import { Button, Input, Card, Badge, Modal, Alert, Switch } from '../../components/UI'
import { PageHeader } from '../../components/Common'

const AdminBioTree = () => {
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedLink, setSelectedLink] = useState(null)
  const [newLink, setNewLink] = useState({
    title: '',
    url: '',
    description: '',
    type: 'website',
    isActive: true
  })

  // بيانات وهمية لروابط Bio Tree
  const [bioLinks, setBioLinks] = useState([
    {
      id: 1,
      title: 'الموقع الرسمي',
      url: 'https://deputy-website.com',
      description: 'الموقع الرسمي للنائب محمد الخالدي',
      type: 'website',
      icon: Globe,
      isActive: true,
      clicks: 1250,
      order: 1
    },
    {
      id: 2,
      title: 'صفحة الفيسبوك',
      url: 'https://facebook.com/deputy.alkhalidi',
      description: 'تابعني على الفيسبوك للحصول على آخر الأخبار',
      type: 'facebook',
      icon: Facebook,
      isActive: true,
      clicks: 890,
      order: 2
    },
    {
      id: 3,
      title: 'حساب تويتر',
      url: 'https://twitter.com/deputy_alkhalidi',
      description: 'آخر التحديثات والتغريدات',
      type: 'twitter',
      icon: Twitter,
      isActive: true,
      clicks: 650,
      order: 3
    },
    {
      id: 4,
      title: 'قناة اليوتيوب',
      url: 'https://youtube.com/c/DeputyAlkhalidi',
      description: 'مقاطع فيديو من الأنشطة والفعاليات',
      type: 'youtube',
      icon: Youtube,
      isActive: false,
      clicks: 320,
      order: 4
    }
  ])

  const linkTypes = [
    { value: 'website', label: 'موقع إلكتروني', icon: Globe },
    { value: 'facebook', label: 'فيسبوك', icon: Facebook },
    { value: 'twitter', label: 'تويتر', icon: Twitter },
    { value: 'instagram', label: 'إنستغرام', icon: Instagram },
    { value: 'youtube', label: 'يوتيوب', icon: Youtube },
    { value: 'linkedin', label: 'لينكد إن', icon: Linkedin },
    { value: 'other', label: 'أخرى', icon: Link }
  ]

  const bioTreeUrl = 'https://bio.deputy-alkhalidi.com'
  const totalClicks = bioLinks.reduce((sum, link) => sum + link.clicks, 0)

  const handleAddLink = () => {
    const newId = Math.max(...bioLinks.map(l => l.id)) + 1
    const newOrder = bioLinks.length + 1
    setBioLinks(prev => [...prev, {
      ...newLink,
      id: newId,
      order: newOrder,
      clicks: 0,
      icon: linkTypes.find(t => t.value === newLink.type)?.icon || Link
    }])
    setNewLink({
      title: '',
      url: '',
      description: '',
      type: 'website',
      isActive: true
    })
    setShowAddModal(false)
  }

  const handleEditLink = (link) => {
    setSelectedLink(link)
    setNewLink({
      title: link.title,
      url: link.url,
      description: link.description,
      type: link.type,
      isActive: link.isActive
    })
    setShowEditModal(true)
  }

  const handleUpdateLink = () => {
    setBioLinks(prev => prev.map(link => 
      link.id === selectedLink.id 
        ? {
            ...link,
            ...newLink,
            icon: linkTypes.find(t => t.value === newLink.type)?.icon || Link
          }
        : link
    ))
    setShowEditModal(false)
    setSelectedLink(null)
  }

  const handleDeleteLink = (link) => {
    setSelectedLink(link)
    setShowDeleteModal(true)
  }

  const confirmDelete = () => {
    setBioLinks(prev => prev.filter(link => link.id !== selectedLink.id))
    setShowDeleteModal(false)
    setSelectedLink(null)
  }

  const toggleLinkStatus = (linkId) => {
    setBioLinks(prev => prev.map(link => 
      link.id === linkId ? { ...link, isActive: !link.isActive } : link
    ))
  }

  const copyBioTreeUrl = () => {
    navigator.clipboard.writeText(bioTreeUrl)
    // يمكن إضافة تنبيه هنا
  }

  const getTypeIcon = (type) => {
    const linkType = linkTypes.find(t => t.value === type)
    return linkType?.icon || Link
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <PageHeader
        title="إدارة Bio Tree"
        description="إدارة الروابط في صفحة Bio Tree الخاصة بك"
        action={
          <div className="flex space-x-2 space-x-reverse">
            <Button variant="outline" onClick={copyBioTreeUrl}>
              <Copy className="w-5 h-5" />
              <span className="mr-2">نسخ الرابط</span>
            </Button>
            <Button variant="primary" onClick={() => setShowAddModal(true)}>
              <Plus className="w-5 h-5" />
              <span className="mr-2">إضافة رابط جديد</span>
            </Button>
          </div>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* العمود الرئيسي - إدارة الروابط */}
        <div className="lg:col-span-2 space-y-6">
          {/* معلومات Bio Tree */}
          <Card>
            <Card.Content className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    رابط Bio Tree الخاص بك
                  </h3>
                  <p className="text-gray-600 text-sm">
                    شارك هذا الرابط مع متابعيك للوصول إلى جميع روابطك
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  <ExternalLink className="w-4 h-4" />
                  <span className="mr-2">معاينة</span>
                </Button>
              </div>
              
              <div className="flex items-center space-x-2 space-x-reverse bg-gray-50 rounded-lg p-3">
                <Input
                  value={bioTreeUrl}
                  readOnly
                  className="flex-1 bg-transparent border-none"
                />
                <Button variant="ghost" size="sm" onClick={copyBioTreeUrl}>
                  <Copy className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </Card.Content>
          </Card>

          {/* قائمة الروابط */}
          <Card>
            <Card.Header>
              <Card.Title>إدارة الروابط</Card.Title>
            </Card.Header>
            <Card.Content className="p-6">
              <div className="space-y-4">
                {bioLinks.length === 0 ? (
                  <div className="text-center py-8">
                    <Link className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      لا توجد روابط
                    </h3>
                    <p className="text-gray-600 mb-4">
                      ابدأ بإضافة روابطك المهمة
                    </p>
                    <Button variant="primary" onClick={() => setShowAddModal(true)}>
                      إضافة أول رابط
                    </Button>
                  </div>
                ) : (
                  bioLinks
                    .sort((a, b) => a.order - b.order)
                    .map(link => {
                      const Icon = link.icon
                      return (
                        <div 
                          key={link.id}
                          className={`border rounded-lg p-4 ${
                            link.isActive ? 'bg-white' : 'bg-gray-50 opacity-60'
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3 space-x-reverse flex-1">
                              <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                                link.isActive ? 'bg-iraqi-blue text-white' : 'bg-gray-300 text-gray-500'
                              }`}>
                                <Icon className="w-5 h-5" />
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 space-x-reverse mb-1">
                                  <h4 className="font-medium text-gray-900">
                                    {link.title}
                                  </h4>
                                  <Badge variant={link.isActive ? 'success' : 'secondary'} size="sm">
                                    {link.isActive ? 'نشط' : 'معطل'}
                                  </Badge>
                                </div>
                                
                                <p className="text-sm text-gray-600 mb-2">
                                  {link.description}
                                </p>
                                
                                <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                                  <span className="truncate">{link.url}</span>
                                  <span>•</span>
                                  <span>{link.clicks} نقرة</span>
                                </div>
                              </div>
                            </div>

                            {/* أزرار الإجراءات */}
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <Switch
                                checked={link.isActive}
                                onChange={() => toggleLinkStatus(link.id)}
                                size="sm"
                              />
                              <Button variant="ghost" size="sm">
                                <Move className="w-4 h-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleEditLink(link)}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleDeleteLink(link)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      )
                    })
                )}
              </div>
            </Card.Content>
          </Card>
        </div>

        {/* العمود الجانبي - الإحصائيات */}
        <div className="space-y-6">
          {/* إحصائيات عامة */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center space-x-2 space-x-reverse">
                <BarChart3 className="w-5 h-5" />
                <span>الإحصائيات</span>
              </Card.Title>
            </Card.Header>
            <Card.Content className="p-6 space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-iraqi-blue mb-1">
                  {totalClicks.toLocaleString()}
                </div>
                <div className="text-sm text-gray-600">
                  إجمالي النقرات
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {bioLinks.filter(l => l.isActive).length}
                </div>
                <div className="text-sm text-gray-600">
                  روابط نشطة
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {bioLinks.length}
                </div>
                <div className="text-sm text-gray-600">
                  إجمالي الروابط
                </div>
              </div>
            </Card.Content>
          </Card>

          {/* أفضل الروابط */}
          <Card>
            <Card.Header>
              <Card.Title>أفضل الروابط</Card.Title>
            </Card.Header>
            <Card.Content className="p-6">
              <div className="space-y-3">
                {bioLinks
                  .filter(l => l.isActive)
                  .sort((a, b) => b.clicks - a.clicks)
                  .slice(0, 5)
                  .map((link, index) => {
                    const Icon = link.icon
                    return (
                      <div key={link.id} className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-8 h-8 bg-iraqi-blue/10 rounded-lg flex items-center justify-center">
                          <Icon className="w-4 h-4 text-iraqi-blue" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-gray-900 truncate">
                            {link.title}
                          </div>
                          <div className="text-xs text-gray-500">
                            {link.clicks} نقرة
                          </div>
                        </div>
                        <div className="text-sm font-medium text-iraqi-blue">
                          #{index + 1}
                        </div>
                      </div>
                    )
                  })}
              </div>
            </Card.Content>
          </Card>
        </div>
      </div>

      {/* مودال إضافة رابط جديد */}
      <Modal 
        isOpen={showAddModal} 
        onClose={() => setShowAddModal(false)}
        title="إضافة رابط جديد"
      >
        <div className="space-y-4">
          <Input
            label="عنوان الرابط"
            value={newLink.title}
            onChange={(e) => setNewLink(prev => ({ ...prev, title: e.target.value }))}
            placeholder="مثال: الموقع الرسمي"
            required
          />
          
          <Input
            label="الرابط"
            value={newLink.url}
            onChange={(e) => setNewLink(prev => ({ ...prev, url: e.target.value }))}
            placeholder="https://example.com"
            required
          />
          
          <Input
            label="الوصف (اختياري)"
            value={newLink.description}
            onChange={(e) => setNewLink(prev => ({ ...prev, description: e.target.value }))}
            placeholder="وصف مختصر للرابط"
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              نوع الرابط
            </label>
            <select
              value={newLink.type}
              onChange={(e) => setNewLink(prev => ({ ...prev, type: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
            >
              {linkTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse">
            <Switch
              checked={newLink.isActive}
              onChange={(checked) => setNewLink(prev => ({ ...prev, isActive: checked }))}
            />
            <label className="text-sm text-gray-700">
              تفعيل الرابط
            </label>
          </div>

          <div className="flex justify-end space-x-3 space-x-reverse">
            <Button 
              variant="ghost" 
              onClick={() => setShowAddModal(false)}
            >
              إلغاء
            </Button>
            <Button 
              variant="primary" 
              onClick={handleAddLink}
              disabled={!newLink.title || !newLink.url}
            >
              إضافة الرابط
            </Button>
          </div>
        </div>
      </Modal>

      {/* مودال تعديل الرابط */}
      <Modal 
        isOpen={showEditModal} 
        onClose={() => setShowEditModal(false)}
        title="تعديل الرابط"
      >
        <div className="space-y-4">
          <Input
            label="عنوان الرابط"
            value={newLink.title}
            onChange={(e) => setNewLink(prev => ({ ...prev, title: e.target.value }))}
            required
          />
          
          <Input
            label="الرابط"
            value={newLink.url}
            onChange={(e) => setNewLink(prev => ({ ...prev, url: e.target.value }))}
            required
          />
          
          <Input
            label="الوصف (اختياري)"
            value={newLink.description}
            onChange={(e) => setNewLink(prev => ({ ...prev, description: e.target.value }))}
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              نوع الرابط
            </label>
            <select
              value={newLink.type}
              onChange={(e) => setNewLink(prev => ({ ...prev, type: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
            >
              {linkTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
          
          <div className="flex items-center space-x-2 space-x-reverse">
            <Switch
              checked={newLink.isActive}
              onChange={(checked) => setNewLink(prev => ({ ...prev, isActive: checked }))}
            />
            <label className="text-sm text-gray-700">
              تفعيل الرابط
            </label>
          </div>

          <div className="flex justify-end space-x-3 space-x-reverse">
            <Button 
              variant="ghost" 
              onClick={() => setShowEditModal(false)}
            >
              إلغاء
            </Button>
            <Button 
              variant="primary" 
              onClick={handleUpdateLink}
              disabled={!newLink.title || !newLink.url}
            >
              حفظ التغييرات
            </Button>
          </div>
        </div>
      </Modal>

      {/* مودال تأكيد الحذف */}
      <Modal 
        isOpen={showDeleteModal} 
        onClose={() => setShowDeleteModal(false)}
        title="تأكيد الحذف"
      >
        <div className="space-y-4">
          <Alert variant="warning" title="تحذير">
            هل أنت متأكد من حذف هذا الرابط؟ لا يمكن التراجع عن هذا الإجراء.
          </Alert>
          
          {selectedLink && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">
                {selectedLink.title}
              </h4>
              <p className="text-sm text-gray-600 mb-2">
                {selectedLink.description}
              </p>
              <p className="text-sm text-gray-500">
                {selectedLink.url}
              </p>
            </div>
          )}

          <div className="flex justify-end space-x-3 space-x-reverse">
            <Button 
              variant="ghost" 
              onClick={() => setShowDeleteModal(false)}
            >
              إلغاء
            </Button>
            <Button 
              variant="danger" 
              onClick={confirmDelete}
            >
              حذف الرابط
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default AdminBioTree
