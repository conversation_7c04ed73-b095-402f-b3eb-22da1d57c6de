import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { Loading } from '../UI'

const ProtectedRoute = ({ children, requiredRole = null }) => {
  const { user, loading } = useAuth()
  const location = useLocation()

  // إذا كان النظام يتحقق من المصادقة
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Loading 
          variant="spinner" 
          size="lg" 
          text="جاري التحقق من الصلاحيات..." 
        />
      </div>
    )
  }

  // إذا لم يكن المستخدم مسجل دخول
  if (!user) {
    return (
      <Navigate 
        to="/admin/login" 
        state={{ from: location }} 
        replace 
      />
    )
  }

  // التحقق من الدور المطلوب (إذا تم تحديده)
  if (requiredRole && user.role !== requiredRole) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            غير مخول للوصول
          </h2>
          <p className="text-gray-600 mb-6">
            ليس لديك الصلاحيات اللازمة للوصول إلى هذه الصفحة
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-iraqi-blue text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            العودة للخلف
          </button>
        </div>
      </div>
    )
  }

  // إذا كان كل شيء صحيح، عرض المحتوى
  return children
}

export default ProtectedRoute
