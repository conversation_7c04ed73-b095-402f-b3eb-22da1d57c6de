import React from 'react'
import { ChevronLeft, Home } from 'lucide-react'
import { Link } from 'react-router-dom'

const PageHeader = ({
  title,
  subtitle,
  breadcrumbs = [],
  actions,
  backgroundImage,
  gradient = true,
  className = '',
  ...props
}) => {
  const backgroundStyle = backgroundImage 
    ? { backgroundImage: `url(${backgroundImage})` }
    : {}
  
  const gradientClass = gradient 
    ? 'bg-gradient-to-r from-iraqi-blue to-iraqi-gold'
    : 'bg-iraqi-blue'
  
  return (
    <div 
      className={`relative ${gradientClass} text-white py-12 ${className}`}
      style={backgroundStyle}
      {...props}
    >
      {/* طبقة التراكب للصورة الخلفية */}
      {backgroundImage && (
        <div className="absolute inset-0 bg-black bg-opacity-50"></div>
      )}
      
      <div className="container-custom relative z-10">
        {/* مسار التنقل */}
        {breadcrumbs.length > 0 && (
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 space-x-reverse text-sm">
              <li>
                <Link 
                  to="/" 
                  className="flex items-center text-blue-200 hover:text-white transition-colors"
                >
                  <Home className="w-4 h-4 ml-1" />
                  الرئيسية
                </Link>
              </li>
              
              {breadcrumbs.map((crumb, index) => (
                <li key={index} className="flex items-center">
                  <ChevronLeft className="w-4 h-4 text-blue-300 mx-2" />
                  {crumb.href ? (
                    <Link 
                      to={crumb.href}
                      className="text-blue-200 hover:text-white transition-colors"
                    >
                      {crumb.label}
                    </Link>
                  ) : (
                    <span className="text-white font-medium">
                      {crumb.label}
                    </span>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        )}
        
        {/* المحتوى الرئيسي */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div className="flex-1">
            <h1 className="text-4xl lg:text-5xl font-bold mb-4 animate-fade-in">
              {title}
            </h1>
            
            {subtitle && (
              <p className="text-xl text-blue-100 max-w-3xl animate-slide-up">
                {subtitle}
              </p>
            )}
          </div>
          
          {/* الإجراءات */}
          {actions && (
            <div className="mt-6 lg:mt-0 lg:mr-8 animate-fade-in">
              {actions}
            </div>
          )}
        </div>
      </div>
      
      {/* تأثير الموجة في الأسفل */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg 
          viewBox="0 0 1200 120" 
          preserveAspectRatio="none" 
          className="relative block w-full h-16"
        >
          <path 
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z" 
            className="fill-current text-gray-50"
          ></path>
        </svg>
      </div>
    </div>
  )
}

export default PageHeader
