import React, { useState } from 'react'
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  Calendar,
  Tag,
  User
} from 'lucide-react'
import { Button, Input, Card, Badge, Modal, Alert } from '../../components/UI'
import { PageHeader } from '../../components/Common'

const AdminNews = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedNews, setSelectedNews] = useState(null)

  // بيانات وهمية للأخبار
  const [newsList, setNewsList] = useState([
    {
      id: 1,
      title: 'اجتماع اللجنة البرلمانية لمناقشة قانون الموازنة العامة',
      excerpt: 'عقدت اللجنة البرلمانية اجتماعاً مهماً لمناقشة بنود قانون الموازنة العامة للعام الحالي...',
      category: 'قوانين',
      status: 'published',
      author: 'مدير التحرير',
      publishDate: '2025-01-15',
      views: 1250,
      image: '/images/news/news1.jpg'
    },
    {
      id: 2,
      title: 'زيارة ميدانية لمشروع تطوير البنية التحتية في بغداد',
      excerpt: 'قام النائب بزيارة ميدانية لمتابعة سير العمل في مشروع تطوير البنية التحتية...',
      category: 'زيارات',
      status: 'published',
      author: 'مدير التحرير',
      publishDate: '2025-01-14',
      views: 890,
      image: '/images/news/news2.jpg'
    },
    {
      id: 3,
      title: 'مؤتمر صحفي حول التطورات السياسية الأخيرة',
      excerpt: 'عقد النائب مؤتمراً صحفياً للحديث عن آخر التطورات السياسية في البلاد...',
      category: 'مؤتمرات',
      status: 'draft',
      author: 'مدير التحرير',
      publishDate: '2025-01-13',
      views: 0,
      image: '/images/news/news3.jpg'
    }
  ])

  const categories = [
    { value: 'all', label: 'جميع الفئات' },
    { value: 'قوانين', label: 'قوانين' },
    { value: 'زيارات', label: 'زيارات' },
    { value: 'اجتماعات', label: 'اجتماعات' },
    { value: 'مؤتمرات', label: 'مؤتمرات' },
    { value: 'بيانات', label: 'بيانات' }
  ]

  const statusOptions = [
    { value: 'published', label: 'منشور', color: 'success' },
    { value: 'draft', label: 'مسودة', color: 'warning' },
    { value: 'archived', label: 'مؤرشف', color: 'secondary' }
  ]

  // تصفية الأخبار
  const filteredNews = newsList.filter(news => {
    const matchesSearch = news.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         news.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || news.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleDeleteNews = (news) => {
    setSelectedNews(news)
    setShowDeleteModal(true)
  }

  const confirmDelete = () => {
    setNewsList(prev => prev.filter(news => news.id !== selectedNews.id))
    setShowDeleteModal(false)
    setSelectedNews(null)
  }

  const getStatusBadge = (status) => {
    const statusConfig = statusOptions.find(s => s.value === status)
    return (
      <Badge variant={statusConfig?.color || 'secondary'}>
        {statusConfig?.label || status}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <PageHeader
        title="إدارة الأخبار"
        description="إدارة وتحرير أخبار الموقع"
        action={
          <Button variant="primary" size="md">
            <Plus className="w-5 h-5" />
            <span className="mr-2">إضافة خبر جديد</span>
          </Button>
        }
      />

      {/* أدوات البحث والتصفية */}
      <Card>
        <Card.Content className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="البحث في الأخبار..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>

            {/* تصفية الفئات */}
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
            >
              {categories.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>

            {/* إحصائيات سريعة */}
            <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
              <span>المجموع: {filteredNews.length}</span>
              <span>منشور: {filteredNews.filter(n => n.status === 'published').length}</span>
              <span>مسودة: {filteredNews.filter(n => n.status === 'draft').length}</span>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* قائمة الأخبار */}
      <div className="grid gap-6">
        {filteredNews.length === 0 ? (
          <Card>
            <Card.Content className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                لا توجد أخبار
              </h3>
              <p className="text-gray-600">
                لم يتم العثور على أخبار تطابق معايير البحث
              </p>
            </Card.Content>
          </Card>
        ) : (
          filteredNews.map(news => (
            <Card key={news.id} className="hover:shadow-lg transition-shadow">
              <Card.Content className="p-6">
                <div className="flex items-start space-x-4 space-x-reverse">
                  {/* صورة الخبر */}
                  <div className="w-24 h-24 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden">
                    {news.image ? (
                      <img 
                        src={news.image} 
                        alt={news.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Eye className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* محتوى الخبر */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                        {news.title}
                      </h3>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {getStatusBadge(news.status)}
                      </div>
                    </div>

                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                      {news.excerpt}
                    </p>

                    {/* معلومات إضافية */}
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <Tag className="w-4 h-4" />
                          <span>{news.category}</span>
                        </div>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <User className="w-4 h-4" />
                          <span>{news.author}</span>
                        </div>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(news.publishDate).toLocaleDateString('ar-EG')}</span>
                        </div>
                        <div className="flex items-center space-x-1 space-x-reverse">
                          <Eye className="w-4 h-4" />
                          <span>{news.views.toLocaleString()} مشاهدة</span>
                        </div>
                      </div>

                      {/* أزرار الإجراءات */}
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => handleDeleteNews(news)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </Card.Content>
            </Card>
          ))
        )}
      </div>

      {/* مودال تأكيد الحذف */}
      <Modal 
        isOpen={showDeleteModal} 
        onClose={() => setShowDeleteModal(false)}
        title="تأكيد الحذف"
      >
        <div className="space-y-4">
          <Alert variant="warning" title="تحذير">
            هل أنت متأكد من حذف هذا الخبر؟ لا يمكن التراجع عن هذا الإجراء.
          </Alert>
          
          {selectedNews && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">
                {selectedNews.title}
              </h4>
              <p className="text-sm text-gray-600">
                {selectedNews.excerpt}
              </p>
            </div>
          )}

          <div className="flex justify-end space-x-3 space-x-reverse">
            <Button 
              variant="ghost" 
              onClick={() => setShowDeleteModal(false)}
            >
              إلغاء
            </Button>
            <Button 
              variant="danger" 
              onClick={confirmDelete}
            >
              حذف الخبر
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default AdminNews
