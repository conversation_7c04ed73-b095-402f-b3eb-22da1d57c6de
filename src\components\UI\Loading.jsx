import React from 'react'
import { Loader2 } from 'lucide-react'

const Loading = ({
  size = 'md',
  variant = 'spinner',
  text,
  fullScreen = false,
  className = '',
  ...props
}) => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }
  
  const textSizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }
  
  const SpinnerComponent = () => (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`} {...props}>
      <Loader2 className={`${sizes[size]} text-iraqi-blue animate-spin`} />
      {text && (
        <p className={`text-gray-600 ${textSizes[size]}`}>{text}</p>
      )}
    </div>
  )
  
  const DotsComponent = () => (
    <div className={`flex items-center justify-center space-x-1 space-x-reverse ${className}`} {...props}>
      <div className="flex space-x-1 space-x-reverse">
        <div className="w-2 h-2 bg-iraqi-blue rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-2 h-2 bg-iraqi-blue rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-2 h-2 bg-iraqi-blue rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
      </div>
      {text && (
        <p className={`text-gray-600 ${textSizes[size]} mr-3`}>{text}</p>
      )}
    </div>
  )
  
  const PulseComponent = () => (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`} {...props}>
      <div className={`${sizes[size]} bg-iraqi-blue rounded-full animate-pulse`}></div>
      {text && (
        <p className={`text-gray-600 ${textSizes[size]}`}>{text}</p>
      )}
    </div>
  )
  
  const ProgressComponent = () => (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`} {...props}>
      <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
        <div className="h-full bg-gradient-to-r from-iraqi-blue to-iraqi-gold rounded-full animate-pulse"></div>
      </div>
      {text && (
        <p className={`text-gray-600 ${textSizes[size]}`}>{text}</p>
      )}
    </div>
  )
  
  const renderVariant = () => {
    switch (variant) {
      case 'dots':
        return <DotsComponent />
      case 'pulse':
        return <PulseComponent />
      case 'progress':
        return <ProgressComponent />
      default:
        return <SpinnerComponent />
    }
  }
  
  if (fullScreen) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-80 backdrop-blur-sm flex items-center justify-center z-50">
        {renderVariant()}
      </div>
    )
  }
  
  return renderVariant()
}

export default Loading
