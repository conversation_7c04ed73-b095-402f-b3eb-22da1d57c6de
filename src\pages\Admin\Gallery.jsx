import React, { useState } from 'react'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye,
  Upload,
  Image as ImageIcon,
  Video,
  Calendar,
  Tag
} from 'lucide-react'
import { Button, Input, Card, Badge, Modal, Alert } from '../../components/UI'
import { PageHeader } from '../../components/Common'

const AdminGallery = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('all')
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)

  // بيانات وهمية للمعرض
  const [galleryItems, setGalleryItems] = useState([
    {
      id: 1,
      title: 'اجتماع اللجنة البرلمانية',
      description: 'صور من اجتماع اللجنة البرلمانية لمناقشة قانون الموازنة',
      type: 'image',
      url: '/images/gallery/meeting1.jpg',
      thumbnail: '/images/gallery/meeting1_thumb.jpg',
      category: 'اجتماعات',
      uploadDate: '2025-01-15',
      size: '2.5 MB',
      views: 450
    },
    {
      id: 2,
      title: 'زيارة مشروع البنية التحتية',
      description: 'فيديو من الزيارة الميدانية لمشروع تطوير البنية التحتية',
      type: 'video',
      url: '/videos/gallery/infrastructure.mp4',
      thumbnail: '/images/gallery/infrastructure_thumb.jpg',
      category: 'زيارات',
      uploadDate: '2025-01-14',
      size: '45.2 MB',
      duration: '5:30',
      views: 1200
    },
    {
      id: 3,
      title: 'المؤتمر الصحفي',
      description: 'صور من المؤتمر الصحفي حول التطورات السياسية',
      type: 'image',
      url: '/images/gallery/press_conference.jpg',
      thumbnail: '/images/gallery/press_conference_thumb.jpg',
      category: 'مؤتمرات',
      uploadDate: '2025-01-13',
      size: '3.1 MB',
      views: 680
    }
  ])

  const typeOptions = [
    { value: 'all', label: 'جميع الأنواع' },
    { value: 'image', label: 'صور', icon: ImageIcon },
    { value: 'video', label: 'فيديوهات', icon: Video }
  ]

  const categories = [
    'اجتماعات',
    'زيارات',
    'مؤتمرات',
    'فعاليات',
    'مشاريع',
    'أنشطة'
  ]

  // تصفية عناصر المعرض
  const filteredItems = galleryItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || item.type === selectedType
    return matchesSearch && matchesType
  })

  const handleDeleteItem = (item) => {
    setSelectedItem(item)
    setShowDeleteModal(true)
  }

  const confirmDelete = () => {
    setGalleryItems(prev => prev.filter(item => item.id !== selectedItem.id))
    setShowDeleteModal(false)
    setSelectedItem(null)
  }

  const formatFileSize = (size) => {
    return size
  }

  const getTypeIcon = (type) => {
    return type === 'video' ? Video : ImageIcon
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <PageHeader
        title="إدارة المعرض"
        description="إدارة الصور والفيديوهات في معرض الموقع"
        action={
          <div className="flex space-x-2 space-x-reverse">
            <Button variant="outline" size="md">
              <Upload className="w-5 h-5" />
              <span className="mr-2">رفع ملفات</span>
            </Button>
            <Button variant="primary" size="md">
              <Plus className="w-5 h-5" />
              <span className="mr-2">إضافة عنصر جديد</span>
            </Button>
          </div>
        }
      />

      {/* أدوات البحث والتصفية */}
      <Card>
        <Card.Content className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="البحث في المعرض..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>

            {/* تصفية النوع */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
            >
              {typeOptions.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>

            {/* إحصائيات سريعة */}
            <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
              <span>المجموع: {filteredItems.length}</span>
              <span>صور: {filteredItems.filter(i => i.type === 'image').length}</span>
              <span>فيديو: {filteredItems.filter(i => i.type === 'video').length}</span>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* شبكة المعرض */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredItems.length === 0 ? (
          <div className="col-span-full">
            <Card>
              <Card.Content className="p-12 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <ImageIcon className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  لا توجد عناصر
                </h3>
                <p className="text-gray-600">
                  لم يتم العثور على عناصر تطابق معايير البحث
                </p>
              </Card.Content>
            </Card>
          </div>
        ) : (
          filteredItems.map(item => {
            const TypeIcon = getTypeIcon(item.type)
            return (
              <Card key={item.id} className="hover:shadow-lg transition-shadow overflow-hidden">
                {/* صورة مصغرة */}
                <div className="relative aspect-video bg-gray-200 overflow-hidden">
                  {item.thumbnail ? (
                    <img 
                      src={item.thumbnail} 
                      alt={item.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <TypeIcon className="w-12 h-12 text-gray-400" />
                    </div>
                  )}
                  
                  {/* نوع الملف */}
                  <div className="absolute top-2 right-2">
                    <Badge 
                      variant={item.type === 'video' ? 'danger' : 'primary'}
                      className="flex items-center space-x-1 space-x-reverse"
                    >
                      <TypeIcon className="w-3 h-3" />
                      <span>{item.type === 'video' ? 'فيديو' : 'صورة'}</span>
                    </Badge>
                  </div>

                  {/* مدة الفيديو */}
                  {item.type === 'video' && item.duration && (
                    <div className="absolute bottom-2 left-2">
                      <Badge variant="dark" className="text-xs">
                        {item.duration}
                      </Badge>
                    </div>
                  )}

                  {/* أزرار الإجراءات */}
                  <div className="absolute top-2 left-2 flex space-x-1 space-x-reverse opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button variant="ghost" size="sm" className="bg-white/80 hover:bg-white">
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="bg-white/80 hover:bg-white">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleDeleteItem(item)}
                      className="bg-white/80 hover:bg-white text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* محتوى البطاقة */}
                <Card.Content className="p-4">
                  <div className="space-y-3">
                    {/* العنوان والوصف */}
                    <div>
                      <h3 className="font-semibold text-gray-900 line-clamp-1">
                        {item.title}
                      </h3>
                      <p className="text-sm text-gray-600 line-clamp-2 mt-1">
                        {item.description}
                      </p>
                    </div>

                    {/* الفئة */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Tag className="w-4 h-4 text-gray-400" />
                      <Badge variant="outline" className="text-xs">
                        {item.category}
                      </Badge>
                    </div>

                    {/* معلومات الملف */}
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Calendar className="w-3 h-3" />
                        <span>{new Date(item.uploadDate).toLocaleDateString('ar-EG')}</span>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span>{formatFileSize(item.size)}</span>
                        <span>•</span>
                        <span>{item.views} مشاهدة</span>
                      </div>
                    </div>
                  </div>
                </Card.Content>
              </Card>
            )
          })
        )}
      </div>

      {/* مودال تأكيد الحذف */}
      <Modal 
        isOpen={showDeleteModal} 
        onClose={() => setShowDeleteModal(false)}
        title="تأكيد الحذف"
      >
        <div className="space-y-4">
          <Alert variant="warning" title="تحذير">
            هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.
          </Alert>
          
          {selectedItem && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                  {selectedItem.thumbnail ? (
                    <img 
                      src={selectedItem.thumbnail} 
                      alt={selectedItem.title}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <ImageIcon className="w-8 h-8 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">
                    {selectedItem.title}
                  </h4>
                  <p className="text-sm text-gray-600 mb-2">
                    {selectedItem.description}
                  </p>
                  <div className="text-xs text-gray-500">
                    <span>{selectedItem.type === 'video' ? 'فيديو' : 'صورة'}</span> • 
                    <span> {selectedItem.size}</span> • 
                    <span> {selectedItem.views} مشاهدة</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 space-x-reverse">
            <Button 
              variant="ghost" 
              onClick={() => setShowDeleteModal(false)}
            >
              إلغاء
            </Button>
            <Button 
              variant="danger" 
              onClick={confirmDelete}
            >
              حذف العنصر
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default AdminGallery
