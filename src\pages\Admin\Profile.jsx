import React, { useState } from 'react'
import { 
  Save, 
  Upload, 
  Edit, 
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  GraduationCap,
  Award,
  FileText,
  Camera
} from 'lucide-react'
import { Button, Input, Card, Textarea, Alert } from '../../components/UI'
import { PageHeader } from '../../components/Common'

const AdminProfile = () => {
  const [isEditing, setIsEditing] = useState(false)
  const [showSuccessAlert, setShowSuccessAlert] = useState(false)
  
  // بيانات وهمية للملف الشخصي
  const [profileData, setProfileData] = useState({
    // المعلومات الشخصية
    fullName: 'د. محمد أحمد الخالدي',
    title: 'عضو مجلس النواب العراقي',
    email: '<EMAIL>',
    phone: '+964 ************',
    location: 'بغداد، العراق',
    birthDate: '1975-03-15',
    
    // السيرة الذاتية
    biography: 'عضو مجلس النواب العراقي منذ عام 2018، متخصص في القانون الدستوري والإداري. حاصل على دكتوراه في القانون من جامعة بغداد. عمل كمحامٍ ومستشار قانوني لأكثر من 15 عاماً قبل دخول المجال السياسي.',
    
    // التعليم
    education: [
      {
        degree: 'دكتوراه في القانون',
        institution: 'جامعة بغداد - كلية القانون',
        year: '2010',
        description: 'تخصص في القانون الدستوري والإداري'
      },
      {
        degree: 'ماجستير في القانون',
        institution: 'جامعة بغداد - كلية القانون',
        year: '2005',
        description: 'تخصص في القانون المدني'
      }
    ],
    
    // الخبرة المهنية
    experience: [
      {
        position: 'عضو مجلس النواب العراقي',
        organization: 'البرلمان العراقي',
        startYear: '2018',
        endYear: 'الحالي',
        description: 'عضو في لجنة الخدمات والإعمار ولجنة القانونية'
      },
      {
        position: 'مستشار قانوني',
        organization: 'مكتب المحاماة الخالدي',
        startYear: '2010',
        endYear: '2018',
        description: 'تقديم الاستشارات القانونية للشركات والمؤسسات'
      }
    ],
    
    // الإنجازات والجوائز
    achievements: [
      {
        title: 'جائزة أفضل نائب في خدمة المجتمع',
        organization: 'منظمة المجتمع المدني العراقي',
        year: '2023'
      },
      {
        title: 'شهادة تقدير للجهود في مجال التعليم',
        organization: 'وزارة التربية والتعليم',
        year: '2022'
      }
    ],
    
    // الصورة الشخصية
    profileImage: '/images/profile/deputy-photo.jpg'
  })

  const [formData, setFormData] = useState(profileData)

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleArrayFieldChange = (arrayName, index, field, value) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: prev[arrayName].map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const addArrayItem = (arrayName, newItem) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: [...prev[arrayName], newItem]
    }))
  }

  const removeArrayItem = (arrayName, index) => {
    setFormData(prev => ({
      ...prev,
      [arrayName]: prev[arrayName].filter((_, i) => i !== index)
    }))
  }

  const handleSave = () => {
    setProfileData(formData)
    setIsEditing(false)
    setShowSuccessAlert(true)
    setTimeout(() => setShowSuccessAlert(false), 3000)
  }

  const handleCancel = () => {
    setFormData(profileData)
    setIsEditing(false)
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <PageHeader
        title="إدارة الملف الشخصي"
        description="تحديث المعلومات الشخصية والسيرة الذاتية"
        action={
          <div className="flex space-x-2 space-x-reverse">
            {isEditing ? (
              <>
                <Button variant="ghost" onClick={handleCancel}>
                  إلغاء
                </Button>
                <Button variant="primary" onClick={handleSave}>
                  <Save className="w-5 h-5" />
                  <span className="mr-2">حفظ التغييرات</span>
                </Button>
              </>
            ) : (
              <Button variant="primary" onClick={() => setIsEditing(true)}>
                <Edit className="w-5 h-5" />
                <span className="mr-2">تعديل الملف الشخصي</span>
              </Button>
            )}
          </div>
        }
      />

      {/* تنبيه النجاح */}
      {showSuccessAlert && (
        <Alert variant="success" title="تم الحفظ بنجاح">
          تم تحديث الملف الشخصي بنجاح
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* العمود الجانبي - الصورة والمعلومات الأساسية */}
        <div className="space-y-6">
          {/* الصورة الشخصية */}
          <Card>
            <Card.Content className="p-6 text-center">
              <div className="relative inline-block">
                <div className="w-32 h-32 bg-gray-200 rounded-full overflow-hidden mx-auto mb-4">
                  {formData.profileImage ? (
                    <img 
                      src={formData.profileImage} 
                      alt="الصورة الشخصية"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <User className="w-16 h-16 text-gray-400" />
                    </div>
                  )}
                </div>
                {isEditing && (
                  <Button 
                    variant="primary" 
                    size="sm" 
                    className="absolute bottom-0 right-1/2 transform translate-x-1/2"
                  >
                    <Camera className="w-4 h-4" />
                  </Button>
                )}
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">
                {formData.fullName}
              </h3>
              <p className="text-gray-600 mb-4">
                {formData.title}
              </p>
              {isEditing && (
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4" />
                  <span className="mr-2">تغيير الصورة</span>
                </Button>
              )}
            </Card.Content>
          </Card>

          {/* معلومات الاتصال */}
          <Card>
            <Card.Header>
              <Card.Title>معلومات الاتصال</Card.Title>
            </Card.Header>
            <Card.Content className="p-6 space-y-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <Mail className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">{formData.email}</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Phone className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">{formData.phone}</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <MapPin className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">{formData.location}</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Calendar className="w-5 h-5 text-gray-400" />
                <span className="text-gray-900">
                  {new Date(formData.birthDate).toLocaleDateString('ar-EG')}
                </span>
              </div>
            </Card.Content>
          </Card>
        </div>

        {/* العمود الرئيسي - المعلومات التفصيلية */}
        <div className="lg:col-span-2 space-y-6">
          {/* المعلومات الشخصية */}
          <Card>
            <Card.Header>
              <Card.Title>المعلومات الشخصية</Card.Title>
            </Card.Header>
            <Card.Content className="p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="الاسم الكامل"
                  value={formData.fullName}
                  onChange={(e) => handleInputChange('fullName', e.target.value)}
                  disabled={!isEditing}
                />
                <Input
                  label="المنصب"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  disabled={!isEditing}
                />
                <Input
                  label="البريد الإلكتروني"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={!isEditing}
                />
                <Input
                  label="رقم الهاتف"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={!isEditing}
                />
                <Input
                  label="الموقع"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  disabled={!isEditing}
                />
                <Input
                  label="تاريخ الميلاد"
                  type="date"
                  value={formData.birthDate}
                  onChange={(e) => handleInputChange('birthDate', e.target.value)}
                  disabled={!isEditing}
                />
              </div>
            </Card.Content>
          </Card>

          {/* السيرة الذاتية */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center space-x-2 space-x-reverse">
                <FileText className="w-5 h-5" />
                <span>السيرة الذاتية</span>
              </Card.Title>
            </Card.Header>
            <Card.Content className="p-6">
              <Textarea
                label="نبذة شخصية"
                value={formData.biography}
                onChange={(e) => handleInputChange('biography', e.target.value)}
                disabled={!isEditing}
                rows={6}
                placeholder="اكتب نبذة عن سيرتك الذاتية..."
              />
            </Card.Content>
          </Card>

          {/* التعليم */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center space-x-2 space-x-reverse">
                <GraduationCap className="w-5 h-5" />
                <span>التعليم</span>
              </Card.Title>
            </Card.Header>
            <Card.Content className="p-6 space-y-4">
              {formData.education.map((edu, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Input
                      label="الدرجة العلمية"
                      value={edu.degree}
                      onChange={(e) => handleArrayFieldChange('education', index, 'degree', e.target.value)}
                      disabled={!isEditing}
                    />
                    <Input
                      label="المؤسسة التعليمية"
                      value={edu.institution}
                      onChange={(e) => handleArrayFieldChange('education', index, 'institution', e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Input
                      label="سنة التخرج"
                      value={edu.year}
                      onChange={(e) => handleArrayFieldChange('education', index, 'year', e.target.value)}
                      disabled={!isEditing}
                    />
                    {isEditing && (
                      <div className="flex items-end">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => removeArrayItem('education', index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          حذف
                        </Button>
                      </div>
                    )}
                  </div>
                  <Textarea
                    label="الوصف"
                    value={edu.description}
                    onChange={(e) => handleArrayFieldChange('education', index, 'description', e.target.value)}
                    disabled={!isEditing}
                    rows={2}
                  />
                </div>
              ))}
              {isEditing && (
                <Button 
                  variant="outline" 
                  onClick={() => addArrayItem('education', {
                    degree: '',
                    institution: '',
                    year: '',
                    description: ''
                  })}
                >
                  إضافة مؤهل تعليمي
                </Button>
              )}
            </Card.Content>
          </Card>

          {/* الخبرة المهنية */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center space-x-2 space-x-reverse">
                <Briefcase className="w-5 h-5" />
                <span>الخبرة المهنية</span>
              </Card.Title>
            </Card.Header>
            <Card.Content className="p-6 space-y-4">
              {formData.experience.map((exp, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Input
                      label="المنصب"
                      value={exp.position}
                      onChange={(e) => handleArrayFieldChange('experience', index, 'position', e.target.value)}
                      disabled={!isEditing}
                    />
                    <Input
                      label="المؤسسة"
                      value={exp.organization}
                      onChange={(e) => handleArrayFieldChange('experience', index, 'organization', e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <Input
                      label="سنة البداية"
                      value={exp.startYear}
                      onChange={(e) => handleArrayFieldChange('experience', index, 'startYear', e.target.value)}
                      disabled={!isEditing}
                    />
                    <Input
                      label="سنة النهاية"
                      value={exp.endYear}
                      onChange={(e) => handleArrayFieldChange('experience', index, 'endYear', e.target.value)}
                      disabled={!isEditing}
                    />
                    {isEditing && (
                      <div className="flex items-end">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => removeArrayItem('experience', index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          حذف
                        </Button>
                      </div>
                    )}
                  </div>
                  <Textarea
                    label="الوصف"
                    value={exp.description}
                    onChange={(e) => handleArrayFieldChange('experience', index, 'description', e.target.value)}
                    disabled={!isEditing}
                    rows={2}
                  />
                </div>
              ))}
              {isEditing && (
                <Button 
                  variant="outline" 
                  onClick={() => addArrayItem('experience', {
                    position: '',
                    organization: '',
                    startYear: '',
                    endYear: '',
                    description: ''
                  })}
                >
                  إضافة خبرة مهنية
                </Button>
              )}
            </Card.Content>
          </Card>

          {/* الإنجازات والجوائز */}
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center space-x-2 space-x-reverse">
                <Award className="w-5 h-5" />
                <span>الإنجازات والجوائز</span>
              </Card.Title>
            </Card.Header>
            <Card.Content className="p-6 space-y-4">
              {formData.achievements.map((achievement, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Input
                      label="عنوان الإنجاز"
                      value={achievement.title}
                      onChange={(e) => handleArrayFieldChange('achievements', index, 'title', e.target.value)}
                      disabled={!isEditing}
                    />
                    <Input
                      label="الجهة المانحة"
                      value={achievement.organization}
                      onChange={(e) => handleArrayFieldChange('achievements', index, 'organization', e.target.value)}
                      disabled={!isEditing}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <Input
                      label="السنة"
                      value={achievement.year}
                      onChange={(e) => handleArrayFieldChange('achievements', index, 'year', e.target.value)}
                      disabled={!isEditing}
                    />
                    {isEditing && (
                      <div className="flex items-end">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => removeArrayItem('achievements', index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          حذف
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {isEditing && (
                <Button 
                  variant="outline" 
                  onClick={() => addArrayItem('achievements', {
                    title: '',
                    organization: '',
                    year: ''
                  })}
                >
                  إضافة إنجاز
                </Button>
              )}
            </Card.Content>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default AdminProfile
