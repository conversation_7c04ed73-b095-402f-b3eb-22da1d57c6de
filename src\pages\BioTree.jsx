import React, { useState } from 'react'
import { 
  ExternalLink,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Globe,
  Phone,
  Mail,
  MapPin,
  FileText,
  Users,
  Calendar,
  MessageSquare,
  Download,
  Share2,
  Copy,
  Check
} from 'lucide-react'

const BioTree = () => {
  const [copied, setCopied] = useState(false)

  const profileInfo = {
    name: 'النائب العراقي',
    title: 'عضو مجلس النواب العراقي',
    description: 'نائب عن الشعب العراقي، ملتزم بخدمة المواطنين وتطوير البلد',
    avatar: '/images/deputy-avatar.jpg',
    location: 'بغداد، العراق'
  }

  const links = [
    {
      id: 1,
      title: 'الموقع الرسمي',
      description: 'تصفح الموقع الرسمي للنائب',
      url: '#',
      icon: Globe,
      color: 'bg-iraqi-blue',
      category: 'رئيسي'
    },
    {
      id: 2,
      title: 'السيرة الذاتية',
      description: 'تعرف على السيرة الذاتية والإنجازات',
      url: '#/biography',
      icon: FileText,
      color: 'bg-iraqi-green',
      category: 'رئيسي'
    },
    {
      id: 3,
      title: 'الأخبار والأنشطة',
      description: 'آخر الأخبار والفعاليات',
      url: '#/news',
      icon: Calendar,
      color: 'bg-iraqi-gold',
      category: 'رئيسي'
    },
    {
      id: 4,
      title: 'المشاريع والإنجازات',
      description: 'تصفح المشاريع المنجزة والجارية',
      url: '#/projects',
      icon: Users,
      color: 'bg-purple-500',
      category: 'رئيسي'
    },
    {
      id: 5,
      title: 'دعم الناخبين',
      description: 'احصل على الدعم والمساعدة',
      url: '#/support',
      icon: MessageSquare,
      color: 'bg-green-500',
      category: 'خدمات'
    },
    {
      id: 6,
      title: 'تواصل معنا',
      description: 'طرق التواصل المختلفة',
      url: '#/contact',
      icon: Phone,
      color: 'bg-blue-500',
      category: 'تواصل'
    },
    {
      id: 7,
      title: 'Facebook',
      description: 'تابعنا على فيسبوك',
      url: 'https://facebook.com',
      icon: Facebook,
      color: 'bg-blue-600',
      category: 'اجتماعي',
      external: true
    },
    {
      id: 8,
      title: 'Twitter',
      description: 'تابعنا على تويتر',
      url: 'https://twitter.com',
      icon: Twitter,
      color: 'bg-sky-500',
      category: 'اجتماعي',
      external: true
    },
    {
      id: 9,
      title: 'Instagram',
      description: 'صور وفيديوهات من الأنشطة',
      url: 'https://instagram.com',
      icon: Instagram,
      color: 'bg-pink-600',
      category: 'اجتماعي',
      external: true
    },
    {
      id: 10,
      title: 'YouTube',
      description: 'قناة اليوتيوب الرسمية',
      url: 'https://youtube.com',
      icon: Youtube,
      color: 'bg-red-600',
      category: 'اجتماعي',
      external: true
    },
    {
      id: 11,
      title: 'البريد الإلكتروني',
      description: 'راسلنا مباشرة',
      url: 'mailto:<EMAIL>',
      icon: Mail,
      color: 'bg-gray-600',
      category: 'تواصل',
      external: true
    },
    {
      id: 12,
      title: 'تحميل السيرة الذاتية',
      description: 'نسخة PDF من السيرة الذاتية',
      url: '/documents/cv.pdf',
      icon: Download,
      color: 'bg-indigo-500',
      category: 'وثائق',
      external: true
    }
  ]

  const categories = ['الكل', 'رئيسي', 'خدمات', 'تواصل', 'اجتماعي', 'وثائق']
  const [selectedCategory, setSelectedCategory] = useState('الكل')

  const filteredLinks = selectedCategory === 'الكل' 
    ? links 
    : links.filter(link => link.category === selectedCategory)

  const handleShare = async () => {
    const url = window.location.href
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: profileInfo.name,
          text: profileInfo.description,
          url: url
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback to copying URL
      handleCopyLink()
    }
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.log('Error copying to clipboard:', error)
    }
  }

  const handleLinkClick = (link) => {
    // Track link clicks (analytics)
    console.log('Link clicked:', link.title)
    
    if (link.external) {
      window.open(link.url, '_blank', 'noopener,noreferrer')
    } else {
      window.location.href = link.url
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-iraqi-blue via-blue-600 to-iraqi-gold py-12">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* الملف الشخصي */}
        <div className="text-center mb-8">
          <div className="relative inline-block mb-6">
            <div className="w-32 h-32 bg-white rounded-full mx-auto shadow-lg flex items-center justify-center">
              <div className="w-28 h-28 bg-gray-200 rounded-full flex items-center justify-center">
                <Users className="w-16 h-16 text-gray-400" />
              </div>
            </div>
            <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white flex items-center justify-center">
              <Check className="w-4 h-4 text-white" />
            </div>
          </div>
          
          <h1 className="text-3xl font-bold text-white mb-2">
            {profileInfo.name}
          </h1>
          <p className="text-xl text-blue-100 mb-3">
            {profileInfo.title}
          </p>
          <p className="text-blue-200 mb-4 max-w-md mx-auto">
            {profileInfo.description}
          </p>
          <div className="flex items-center justify-center text-blue-200 mb-6">
            <MapPin className="w-4 h-4 ml-1" />
            {profileInfo.location}
          </div>

          {/* أزرار المشاركة */}
          <div className="flex justify-center space-x-4 space-x-reverse mb-8">
            <button
              onClick={handleShare}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all inline-flex items-center"
            >
              <Share2 className="w-4 h-4 ml-2" />
              مشاركة
            </button>
            <button
              onClick={handleCopyLink}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all inline-flex items-center"
            >
              {copied ? (
                <>
                  <Check className="w-4 h-4 ml-2" />
                  تم النسخ
                </>
              ) : (
                <>
                  <Copy className="w-4 h-4 ml-2" />
                  نسخ الرابط
                </>
              )}
            </button>
          </div>
        </div>

        {/* فلاتر الفئات */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                selectedCategory === category
                  ? 'bg-white text-iraqi-blue shadow-lg'
                  : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* قائمة الروابط */}
        <div className="space-y-4">
          {filteredLinks.map((link) => {
            const Icon = link.icon
            return (
              <button
                key={link.id}
                onClick={() => handleLinkClick(link)}
                className="w-full bg-white rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group"
              >
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className={`w-12 h-12 ${link.color} rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1 text-right">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 group-hover:text-iraqi-blue transition-colors">
                        {link.title}
                      </h3>
                      {link.external && (
                        <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-iraqi-blue transition-colors" />
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {link.description}
                    </p>
                  </div>
                </div>
              </button>
            )
          })}
        </div>

        {/* معلومات إضافية */}
        <div className="mt-12 text-center">
          <div className="bg-white bg-opacity-10 rounded-xl p-6 backdrop-blur-sm">
            <h3 className="text-lg font-semibold text-white mb-3">
              معلومات التواصل السريع
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-blue-100">
              <div className="flex items-center justify-center space-x-2 space-x-reverse">
                <Phone className="w-4 h-4" />
                <span className="text-sm">+964-XXX-XXXX-XXX</span>
              </div>
              <div className="flex items-center justify-center space-x-2 space-x-reverse">
                <Mail className="w-4 h-4" />
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات */}
        <div className="mt-8 grid grid-cols-3 gap-4">
          <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center backdrop-blur-sm">
            <h4 className="text-2xl font-bold text-white">50+</h4>
            <p className="text-blue-200 text-sm">مشروع منجز</p>
          </div>
          <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center backdrop-blur-sm">
            <h4 className="text-2xl font-bold text-white">10K+</h4>
            <p className="text-blue-200 text-sm">مواطن مستفيد</p>
          </div>
          <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center backdrop-blur-sm">
            <h4 className="text-2xl font-bold text-white">5+</h4>
            <p className="text-blue-200 text-sm">سنوات خدمة</p>
          </div>
        </div>

        {/* تذييل */}
        <div className="mt-12 text-center text-blue-200 text-sm">
          <p>© 2025 النائب العراقي. جميع الحقوق محفوظة.</p>
          <p className="mt-2">
            تم تطوير هذه الصفحة لتسهيل التواصل مع المواطنين
          </p>
        </div>
      </div>
    </div>
  )
}

export default BioTree
