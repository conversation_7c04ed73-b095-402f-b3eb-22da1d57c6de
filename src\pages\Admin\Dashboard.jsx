import React from 'react'
import { 
  Users, 
  Newspaper, 
  FolderOpen, 
  MessageSquare, 
  TrendingUp,
  Eye,
  Calendar,
  Activity
} from 'lucide-react'
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { Card, Badge } from '../../components/UI'
import { StatsCard } from '../../components/Common'

const Dashboard = () => {
  // بيانات وهمية للإحصائيات
  const stats = [
    {
      title: 'إجمالي الزوار',
      value: '12,847',
      change: '+12.5%',
      changeType: 'increase',
      icon: Users,
      color: 'iraqi-blue'
    },
    {
      title: 'الأخبار المنشورة',
      value: '156',
      change: '+8',
      changeType: 'increase',
      icon: Newspaper,
      color: 'iraqi-green'
    },
    {
      title: 'المشاريع النشطة',
      value: '23',
      change: '+3',
      changeType: 'increase',
      icon: FolderOpen,
      color: 'iraqi-gold'
    },
    {
      title: 'الرسائل الجديدة',
      value: '47',
      change: '+15',
      changeType: 'increase',
      icon: MessageSquare,
      color: 'iraqi-red'
    }
  ]

  // بيانات الزوار الشهرية
  const visitorsData = [
    { month: 'يناير', visitors: 8400, pageViews: 12600 },
    { month: 'فبراير', visitors: 9200, pageViews: 14800 },
    { month: 'مارس', visitors: 10100, pageViews: 16200 },
    { month: 'أبريل', visitors: 11300, pageViews: 18900 },
    { month: 'مايو', visitors: 12000, pageViews: 20400 },
    { month: 'يونيو', visitors: 12847, pageViews: 22100 }
  ]

  // بيانات المحتوى
  const contentData = [
    { name: 'الأخبار', value: 156, color: '#1e40af' },
    { name: 'المشاريع', value: 23, color: '#16a34a' },
    { name: 'الصور', value: 89, color: '#f59e0b' },
    { name: 'الفيديوهات', value: 12, color: '#dc2626' }
  ]

  // بيانات النشاط الأسبوعي
  const weeklyActivity = [
    { day: 'السبت', posts: 4, messages: 12, visitors: 1200 },
    { day: 'الأحد', posts: 6, messages: 18, visitors: 1400 },
    { day: 'الاثنين', posts: 8, messages: 25, visitors: 1800 },
    { day: 'الثلاثاء', posts: 5, messages: 15, visitors: 1600 },
    { day: 'الأربعاء', posts: 7, messages: 22, visitors: 1900 },
    { day: 'الخميس', posts: 9, messages: 28, visitors: 2100 },
    { day: 'الجمعة', posts: 3, messages: 8, visitors: 900 }
  ]

  // الأنشطة الأخيرة
  const recentActivities = [
    {
      id: 1,
      type: 'news',
      title: 'تم نشر خبر جديد',
      description: 'اجتماع اللجنة البرلمانية لمناقشة قانون الموازنة',
      time: 'منذ 30 دقيقة',
      icon: Newspaper,
      color: 'text-blue-600'
    },
    {
      id: 2,
      type: 'message',
      title: 'رسالة جديدة من مواطن',
      description: 'استفسار حول مشروع تطوير البنية التحتية',
      time: 'منذ ساعة',
      icon: MessageSquare,
      color: 'text-green-600'
    },
    {
      id: 3,
      type: 'project',
      title: 'تحديث مشروع',
      description: 'تم تحديث حالة مشروع "تطوير الطرق" إلى مكتمل',
      time: 'منذ 2 ساعة',
      icon: FolderOpen,
      color: 'text-yellow-600'
    },
    {
      id: 4,
      type: 'visitor',
      title: 'زيادة في الزوار',
      description: 'تم تسجيل 500 زائر جديد اليوم',
      time: 'منذ 3 ساعات',
      icon: TrendingUp,
      color: 'text-purple-600'
    }
  ]

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">الداشبورد الرئيسي</h1>
          <p className="text-gray-600 mt-1">نظرة عامة على أداء الموقع والأنشطة</p>
        </div>
        <div className="flex items-center space-x-2 space-x-reverse">
          <Calendar className="w-5 h-5 text-gray-500" />
          <span className="text-sm text-gray-600">
            {new Date().toLocaleDateString('ar-EG', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </span>
        </div>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            icon={stat.icon}
            color={stat.color}
          />
        ))}
      </div>

      {/* الرسوم البيانية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* رسم بياني للزوار */}
        <Card>
          <Card.Header>
            <Card.Title className="flex items-center space-x-2 space-x-reverse">
              <Eye className="w-5 h-5 text-iraqi-blue" />
              <span>إحصائيات الزوار</span>
            </Card.Title>
            <Card.Description>
              عدد الزوار ومشاهدات الصفحات خلال الأشهر الستة الماضية
            </Card.Description>
          </Card.Header>
          <Card.Content>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={visitorsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="visitors" 
                  stackId="1"
                  stroke="#1e40af" 
                  fill="#1e40af" 
                  fillOpacity={0.6}
                  name="الزوار"
                />
                <Area 
                  type="monotone" 
                  dataKey="pageViews" 
                  stackId="1"
                  stroke="#16a34a" 
                  fill="#16a34a" 
                  fillOpacity={0.6}
                  name="مشاهدات الصفحات"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card.Content>
        </Card>

        {/* توزيع المحتوى */}
        <Card>
          <Card.Header>
            <Card.Title className="flex items-center space-x-2 space-x-reverse">
              <Activity className="w-5 h-5 text-iraqi-green" />
              <span>توزيع المحتوى</span>
            </Card.Title>
            <Card.Description>
              توزيع أنواع المحتوى المنشور على الموقع
            </Card.Description>
          </Card.Header>
          <Card.Content>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={contentData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {contentData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card.Content>
        </Card>
      </div>

      {/* النشاط الأسبوعي والأنشطة الأخيرة */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* النشاط الأسبوعي */}
        <div className="lg:col-span-2">
          <Card>
            <Card.Header>
              <Card.Title className="flex items-center space-x-2 space-x-reverse">
                <BarChart className="w-5 h-5 text-iraqi-gold" />
                <span>النشاط الأسبوعي</span>
              </Card.Title>
              <Card.Description>
                نشاط الموقع خلال الأسبوع الحالي
              </Card.Description>
            </Card.Header>
            <Card.Content>
              <ResponsiveContainer width="100%" height={250}>
                <BarChart data={weeklyActivity}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="posts" fill="#1e40af" name="المنشورات" />
                  <Bar dataKey="messages" fill="#16a34a" name="الرسائل" />
                </BarChart>
              </ResponsiveContainer>
            </Card.Content>
          </Card>
        </div>

        {/* الأنشطة الأخيرة */}
        <Card>
          <Card.Header>
            <Card.Title>الأنشطة الأخيرة</Card.Title>
            <Card.Description>
              آخر الأنشطة على الموقع
            </Card.Description>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              {recentActivities.map((activity) => {
                const Icon = activity.icon
                return (
                  <div key={activity.id} className="flex items-start space-x-3 space-x-reverse">
                    <div className={`p-2 rounded-lg bg-gray-100 ${activity.color}`}>
                      <Icon className="w-4 h-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {activity.title}
                      </p>
                      <p className="text-sm text-gray-600 truncate">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {activity.time}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  )
}

export default Dashboard
