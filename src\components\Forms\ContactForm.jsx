import React, { useState } from 'react'
import { Send, User, Mail, Phone, MessageSquare } from 'lucide-react'
import { Input, Select, Textarea, Button, Alert } from '../UI'

const ContactForm = ({
  onSubmit,
  loading = false,
  className = '',
  ...props
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    contactType: 'عام',
    urgency: 'عادي'
  })
  
  const [errors, setErrors] = useState({})
  const [showSuccess, setShowSuccess] = useState(false)
  
  const contactTypes = [
    'عام',
    'استفسار',
    'شكوى',
    'اقتراح',
    'طلب مقابلة',
    'إعلام'
  ]
  
  const urgencyLevels = [
    { value: 'عادي', label: 'عادي' },
    { value: 'مهم', label: 'مهم' },
    { value: 'عاجل', label: 'عاجل' }
  ]
  
  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.name.trim()) {
      newErrors.name = 'الاسم مطلوب'
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'البريد الإلكتروني مطلوب'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'البريد الإلكتروني غير صحيح'
    }
    
    if (!formData.subject.trim()) {
      newErrors.subject = 'الموضوع مطلوب'
    }
    
    if (!formData.message.trim()) {
      newErrors.message = 'الرسالة مطلوبة'
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'الرسالة يجب أن تكون 10 أحرف على الأقل'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }
  
  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // مسح الخطأ عند التعديل
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    try {
      await onSubmit?.(formData)
      setShowSuccess(true)
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
        contactType: 'عام',
        urgency: 'عادي'
      })
      
      // إخفاء رسالة النجاح بعد 5 ثوان
      setTimeout(() => setShowSuccess(false), 5000)
    } catch (error) {
      console.error('Error submitting form:', error)
    }
  }
  
  return (
    <div className={className} {...props}>
      {showSuccess && (
        <Alert 
          variant="success" 
          title="تم الإرسال بنجاح"
          dismissible
          onDismiss={() => setShowSuccess(false)}
          className="mb-6"
        >
          تم إرسال رسالتكم بنجاح. سنقوم بالرد عليكم في أقرب وقت ممكن.
        </Alert>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* المعلومات الشخصية */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="الاسم الكامل"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            error={errors.name}
            required
            icon={User}
            placeholder="أدخل اسمك الكامل"
          />
          
          <Input
            label="البريد الإلكتروني"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleInputChange}
            error={errors.email}
            required
            icon={Mail}
            placeholder="<EMAIL>"
          />
          
          <Input
            label="رقم الهاتف"
            name="phone"
            type="tel"
            value={formData.phone}
            onChange={handleInputChange}
            error={errors.phone}
            icon={Phone}
            placeholder="07XX XXX XXXX"
            helperText="اختياري"
          />
          
          <Select
            label="نوع الرسالة"
            name="contactType"
            value={formData.contactType}
            onChange={handleInputChange}
            options={contactTypes}
          />
        </div>
        
        {/* تفاصيل الرسالة */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            label="الموضوع"
            name="subject"
            value={formData.subject}
            onChange={handleInputChange}
            error={errors.subject}
            required
            placeholder="موضوع الرسالة"
            containerClassName="md:col-span-1"
          />
          
          <Select
            label="مستوى الأولوية"
            name="urgency"
            value={formData.urgency}
            onChange={handleInputChange}
            options={urgencyLevels}
          />
        </div>
        
        <Textarea
          label="الرسالة"
          name="message"
          value={formData.message}
          onChange={handleInputChange}
          error={errors.message}
          required
          rows={6}
          placeholder="اكتب رسالتك هنا..."
          showCharCount
          maxLength={1000}
        />
        
        {/* زر الإرسال */}
        <div className="flex items-center justify-between pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            * الحقول المطلوبة
          </p>
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={loading}
            icon={Send}
            iconPosition="right"
          >
            إرسال الرسالة
          </Button>
        </div>
      </form>
    </div>
  )
}

export default ContactForm
