import React from 'react'
import { Outlet, Navigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import Sidebar from './Sidebar'
import AdminHeader from './AdminHeader'
import { Loading } from '../UI'

const AdminLayout = () => {
  const { user, loading } = useAuth()

  if (loading) {
    return <Loading fullScreen text="جاري التحقق من الصلاحيات..." />
  }

  if (!user) {
    return <Navigate to="/admin/login" replace />
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* الشريط الجانبي */}
      <Sidebar />
      
      {/* المحتوى الرئيسي */}
      <div className="flex-1 flex flex-col">
        {/* رأس لوحة التحكم */}
        <AdminHeader />
        
        {/* منطقة المحتوى */}
        <main className="flex-1 p-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  )
}

export default AdminLayout
