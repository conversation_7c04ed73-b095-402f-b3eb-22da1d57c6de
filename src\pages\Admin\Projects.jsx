import React, { useState } from 'react'
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Eye,
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react'
import { Button, Input, Card, Badge, Modal, Alert } from '../../components/UI'
import { PageHeader } from '../../components/Common'

const AdminProjects = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedProject, setSelectedProject] = useState(null)

  // بيانات وهمية للمشاريع
  const [projectsList, setProjectsList] = useState([
    {
      id: 1,
      title: 'مشروع تطوير البنية التحتية في منطقة الكرادة',
      description: 'مشروع شامل لتطوير البنية التحتية وتحسين الخدمات في منطقة الكرادة',
      location: 'الكرادة، بغداد',
      budget: '2,500,000',
      startDate: '2024-03-15',
      endDate: '2025-06-30',
      status: 'in_progress',
      progress: 65,
      contractor: 'شركة البناء العراقية',
      category: 'بنية تحتية'
    },
    {
      id: 2,
      title: 'إنشاء مدرسة ابتدائية في حي الجادرية',
      description: 'بناء مدرسة ابتدائية حديثة لخدمة أطفال المنطقة',
      location: 'الجادرية، بغداد',
      budget: '800,000',
      startDate: '2024-09-01',
      endDate: '2025-08-31',
      status: 'completed',
      progress: 100,
      contractor: 'شركة المقاولات المتحدة',
      category: 'تعليم'
    },
    {
      id: 3,
      title: 'تأهيل مستشفى الكندي',
      description: 'مشروع تأهيل وتطوير مستشفى الكندي وتحديث المعدات الطبية',
      location: 'الكندي، بغداد',
      budget: '1,200,000',
      startDate: '2025-01-01',
      endDate: '2025-12-31',
      status: 'planned',
      progress: 0,
      contractor: 'شركة الخدمات الطبية',
      category: 'صحة'
    }
  ])

  const statusOptions = [
    { value: 'all', label: 'جميع الحالات' },
    { value: 'planned', label: 'مخطط', color: 'secondary', icon: Clock },
    { value: 'in_progress', label: 'قيد التنفيذ', color: 'warning', icon: AlertCircle },
    { value: 'completed', label: 'مكتمل', color: 'success', icon: CheckCircle },
    { value: 'cancelled', label: 'ملغي', color: 'danger', icon: XCircle }
  ]

  const categories = [
    'بنية تحتية',
    'تعليم',
    'صحة',
    'خدمات',
    'بيئة',
    'رياضة',
    'ثقافة'
  ]

  // تصفية المشاريع
  const filteredProjects = projectsList.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || project.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const handleDeleteProject = (project) => {
    setSelectedProject(project)
    setShowDeleteModal(true)
  }

  const confirmDelete = () => {
    setProjectsList(prev => prev.filter(project => project.id !== selectedProject.id))
    setShowDeleteModal(false)
    setSelectedProject(null)
  }

  const getStatusBadge = (status) => {
    const statusConfig = statusOptions.find(s => s.value === status)
    if (!statusConfig) return null
    
    const Icon = statusConfig.icon
    return (
      <Badge variant={statusConfig.color} className="flex items-center space-x-1 space-x-reverse">
        <Icon className="w-3 h-3" />
        <span>{statusConfig.label}</span>
      </Badge>
    )
  }

  const getProgressColor = (progress) => {
    if (progress === 0) return 'bg-gray-200'
    if (progress < 30) return 'bg-red-500'
    if (progress < 70) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <PageHeader
        title="إدارة المشاريع"
        description="متابعة وإدارة مشاريع التطوير والخدمات"
        action={
          <Button variant="primary" size="md">
            <Plus className="w-5 h-5" />
            <span className="mr-2">إضافة مشروع جديد</span>
          </Button>
        }
      />

      {/* أدوات البحث والتصفية */}
      <Card>
        <Card.Content className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="البحث في المشاريع..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10"
              />
            </div>

            {/* تصفية الحالة */}
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
            >
              {statusOptions.map(status => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>

            {/* إحصائيات سريعة */}
            <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
              <span>المجموع: {filteredProjects.length}</span>
              <span>نشط: {filteredProjects.filter(p => p.status === 'in_progress').length}</span>
              <span>مكتمل: {filteredProjects.filter(p => p.status === 'completed').length}</span>
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* قائمة المشاريع */}
      <div className="grid gap-6">
        {filteredProjects.length === 0 ? (
          <Card>
            <Card.Content className="p-12 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                لا توجد مشاريع
              </h3>
              <p className="text-gray-600">
                لم يتم العثور على مشاريع تطابق معايير البحث
              </p>
            </Card.Content>
          </Card>
        ) : (
          filteredProjects.map(project => (
            <Card key={project.id} className="hover:shadow-lg transition-shadow">
              <Card.Content className="p-6">
                <div className="space-y-4">
                  {/* رأس المشروع */}
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 space-x-reverse mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {project.title}
                        </h3>
                        {getStatusBadge(project.status)}
                      </div>
                      <p className="text-gray-600 text-sm mb-3">
                        {project.description}
                      </p>
                    </div>

                    {/* أزرار الإجراءات */}
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleDeleteProject(project)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  {/* شريط التقدم */}
                  {project.status === 'in_progress' && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">نسبة الإنجاز</span>
                        <span className="font-medium">{project.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(project.progress)}`}
                          style={{ width: `${project.progress}%` }}
                        />
                      </div>
                    </div>
                  )}

                  {/* معلومات المشروع */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span>{project.location}</span>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                      <DollarSign className="w-4 h-4" />
                      <span>{project.budget} دولار</span>
                    </div>
                    <div className="flex items-center space-x-2 space-x-reverse text-gray-600">
                      <Calendar className="w-4 h-4" />
                      <span>
                        {new Date(project.startDate).toLocaleDateString('ar-EG')} - 
                        {new Date(project.endDate).toLocaleDateString('ar-EG')}
                      </span>
                    </div>
                    <div className="text-gray-600">
                      <span className="font-medium">المقاول: </span>
                      {project.contractor}
                    </div>
                  </div>

                  {/* فئة المشروع */}
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-iraqi-blue border-iraqi-blue">
                      {project.category}
                    </Badge>
                    
                    {/* معلومات إضافية حسب الحالة */}
                    {project.status === 'completed' && (
                      <span className="text-sm text-green-600 font-medium">
                        ✓ تم الإنجاز بنجاح
                      </span>
                    )}
                    {project.status === 'planned' && (
                      <span className="text-sm text-gray-600">
                        بدء العمل: {new Date(project.startDate).toLocaleDateString('ar-EG')}
                      </span>
                    )}
                  </div>
                </div>
              </Card.Content>
            </Card>
          ))
        )}
      </div>

      {/* مودال تأكيد الحذف */}
      <Modal 
        isOpen={showDeleteModal} 
        onClose={() => setShowDeleteModal(false)}
        title="تأكيد الحذف"
      >
        <div className="space-y-4">
          <Alert variant="warning" title="تحذير">
            هل أنت متأكد من حذف هذا المشروع؟ لا يمكن التراجع عن هذا الإجراء.
          </Alert>
          
          {selectedProject && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">
                {selectedProject.title}
              </h4>
              <p className="text-sm text-gray-600 mb-2">
                {selectedProject.description}
              </p>
              <div className="text-sm text-gray-500">
                <span>الموقع: {selectedProject.location}</span> • 
                <span> الميزانية: {selectedProject.budget} دولار</span>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 space-x-reverse">
            <Button 
              variant="ghost" 
              onClick={() => setShowDeleteModal(false)}
            >
              إلغاء
            </Button>
            <Button 
              variant="danger" 
              onClick={confirmDelete}
            >
              حذف المشروع
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default AdminProjects
