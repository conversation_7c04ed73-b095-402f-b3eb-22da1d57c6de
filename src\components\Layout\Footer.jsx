import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  Facebook, 
  Twitter, 
  Instagram, 
  Youtube, 
  Phone, 
  Mail, 
  MapPin,
  Heart
} from 'lucide-react'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const quickLinks = [
    { name: 'الرئيسية', href: '/' },
    { name: 'السيرة الذاتية', href: '/biography' },
    { name: 'الأخبار', href: '/news' },
    { name: 'المشاريع', href: '/projects' }
  ]

  const services = [
    { name: 'دعم الناخبين', href: '/support' },
    { name: 'تقديم شكوى', href: '/contact' },
    { name: 'طلب خدمة', href: '/support' },
    { name: 'استفسار', href: '/contact' }
  ]

  const socialLinks = [
    { name: 'Facebook', icon: Facebook, href: '#', color: 'hover:text-blue-600' },
    { name: 'Twitter', icon: Twitter, href: '#', color: 'hover:text-blue-400' },
    { name: 'Instagram', icon: Instagram, href: '#', color: 'hover:text-pink-600' },
    { name: 'YouTube', icon: Youtube, href: '#', color: 'hover:text-red-600' }
  ]

  return (
    <footer className="bg-gray-900 text-white">
      {/* المحتوى الرئيسي */}
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* معلومات النائب */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-iraqi-gold rounded-full flex items-center justify-center">
                <span className="text-white font-bold">ن</span>
              </div>
              <div>
                <h3 className="text-lg font-bold">النائب العراقي</h3>
                <p className="text-sm text-gray-400">خدمة المواطن أولاً</p>
              </div>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              منصة رقمية شاملة لخدمة المواطنين العراقيين وعرض أنشطة النائب بشفافية كاملة.
            </p>
            <div className="flex space-x-4 space-x-reverse">
              {socialLinks.map((social) => {
                const Icon = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.href}
                    className={`text-gray-400 transition-colors duration-200 ${social.color}`}
                    aria-label={social.name}
                  >
                    <Icon className="w-5 h-5" />
                  </a>
                )
              })}
            </div>
          </div>

          {/* روابط سريعة */}
          <div>
            <h4 className="text-lg font-semibold mb-4">روابط سريعة</h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-300 hover:text-iraqi-gold transition-colors duration-200 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* الخدمات */}
          <div>
            <h4 className="text-lg font-semibold mb-4">الخدمات</h4>
            <ul className="space-y-2">
              {services.map((service) => (
                <li key={service.name}>
                  <Link
                    to={service.href}
                    className="text-gray-300 hover:text-iraqi-gold transition-colors duration-200 text-sm"
                  >
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* معلومات التواصل */}
          <div>
            <h4 className="text-lg font-semibold mb-4">تواصل معنا</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <Phone className="w-4 h-4 text-iraqi-gold" />
                <span className="text-gray-300 text-sm">+964-XXX-XXXX-XXX</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Mail className="w-4 h-4 text-iraqi-gold" />
                <span className="text-gray-300 text-sm"><EMAIL></span>
              </div>
              <div className="flex items-start space-x-3 space-x-reverse">
                <MapPin className="w-4 h-4 text-iraqi-gold mt-1" />
                <span className="text-gray-300 text-sm">
                  مجلس النواب العراقي<br />
                  المنطقة الخضراء، بغداد
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* الشريط السفلي */}
      <div className="border-t border-gray-800">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} موقع النائب العراقي الرسمي. جميع الحقوق محفوظة.
            </p>
            <p className="text-gray-400 text-sm mt-2 md:mt-0 flex items-center">
              صُنع بـ <Heart className="w-4 h-4 text-red-500 mx-1" /> في العراق
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
