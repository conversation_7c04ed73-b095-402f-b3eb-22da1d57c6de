import React from 'react'
import { Calendar, Eye, Clock, ArrowLeft } from 'lucide-react'
import { Badge } from '../UI'

const NewsCard = ({
  title,
  excerpt,
  category,
  date,
  time,
  views,
  image,
  featured = false,
  onClick,
  className = '',
  ...props
}) => {
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-IQ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }
  
  const formatViews = (viewCount) => {
    if (viewCount >= 1000) {
      return (viewCount / 1000).toFixed(1) + 'ك'
    }
    return viewCount?.toLocaleString('ar-IQ') || '0'
  }
  
  const getCategoryColor = (category) => {
    const colors = {
      'قوانين': 'primary',
      'زيارات': 'success',
      'اجتماعات': 'warning',
      'بيانات': 'info',
      'فعاليات': 'secondary',
      'مؤتمرات': 'danger'
    }
    return colors[category] || 'default'
  }
  
  return (
    <div 
      className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden cursor-pointer group ${
        featured ? 'ring-2 ring-iraqi-gold' : ''
      } ${className}`}
      onClick={onClick}
      {...props}
    >
      {/* صورة الخبر */}
      {image && (
        <div className="relative h-48 overflow-hidden">
          <img 
            src={image} 
            alt={title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          {featured && (
            <div className="absolute top-3 right-3">
              <Badge variant="secondary" size="sm">
                خبر مميز
              </Badge>
            </div>
          )}
          {category && (
            <div className="absolute bottom-3 right-3">
              <Badge variant={getCategoryColor(category)} size="sm">
                {category}
              </Badge>
            </div>
          )}
        </div>
      )}
      
      <div className="p-6">
        {/* العنوان */}
        <h3 className={`font-bold text-gray-900 mb-3 group-hover:text-iraqi-blue transition-colors line-clamp-2 ${
          featured ? 'text-xl' : 'text-lg'
        }`}>
          {title}
        </h3>
        
        {/* المقتطف */}
        {excerpt && (
          <p className="text-gray-600 mb-4 line-clamp-3 text-sm leading-relaxed">
            {excerpt}
          </p>
        )}
        
        {/* معلومات إضافية */}
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4 space-x-reverse">
            {date && (
              <div className="flex items-center">
                <Calendar className="w-4 h-4 ml-1" />
                <span>{formatDate(date)}</span>
              </div>
            )}
            
            {time && (
              <div className="flex items-center">
                <Clock className="w-4 h-4 ml-1" />
                <span>{time}</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-3 space-x-reverse">
            {views !== undefined && (
              <div className="flex items-center">
                <Eye className="w-4 h-4 ml-1" />
                <span>{formatViews(views)}</span>
              </div>
            )}
            
            <ArrowLeft className="w-4 h-4 text-iraqi-blue group-hover:translate-x-1 transition-transform" />
          </div>
        </div>
      </div>
    </div>
  )
}

export default NewsCard
