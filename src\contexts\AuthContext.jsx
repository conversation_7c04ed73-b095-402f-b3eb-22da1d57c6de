import React, { createContext, useContext, useState, useEffect } from 'react'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [token, setToken] = useState(localStorage.getItem('admin_token'))

  useEffect(() => {
    // التحقق من صحة التوكن عند تحميل التطبيق
    if (token) {
      validateToken()
    } else {
      setLoading(false)
    }
  }, [token])

  const validateToken = async () => {
    try {
      // هنا سيتم التحقق من صحة التوكن مع الخادم
      // مؤقتاً سنستخدم بيانات وهمية
      const userData = {
        id: 1,
        username: 'admin',
        name: 'مدير النظام',
        role: 'admin'
      }
      setUser(userData)
    } catch (error) {
      console.error('Token validation failed:', error)
      logout()
    } finally {
      setLoading(false)
    }
  }

  const login = async (username, password) => {
    try {
      // مؤقتاً سنستخدم بيانات وهمية للتسجيل
      if (username === 'admin' && password === 'admin123') {
        const mockToken = 'mock_jwt_token_' + Date.now()
        const userData = {
          id: 1,
          username: 'admin',
          name: 'مدير النظام',
          role: 'admin'
        }
        
        localStorage.setItem('admin_token', mockToken)
        setToken(mockToken)
        setUser(userData)
        
        return { success: true, user: userData }
      } else {
        throw new Error('بيانات الدخول غير صحيحة')
      }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const logout = () => {
    localStorage.removeItem('admin_token')
    setToken(null)
    setUser(null)
  }

  const value = {
    user,
    token,
    loading,
    login,
    logout,
    isAuthenticated: !!user
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
