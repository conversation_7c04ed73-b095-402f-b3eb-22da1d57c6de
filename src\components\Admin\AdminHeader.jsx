import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { 
  Bell, 
  Search, 
  User, 
  Settings, 
  LogOut,
  Menu,
  Home,
  ChevronDown
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { Button, Badge } from '../UI'

const AdminHeader = () => {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const { user, logout } = useAuth()

  // بيانات وهمية للإشعارات
  const notifications = [
    {
      id: 1,
      title: 'رسالة جديدة من مواطن',
      message: 'تم استلام رسالة جديدة تحتاج للمراجعة',
      time: 'منذ 5 دقائق',
      unread: true
    },
    {
      id: 2,
      title: 'تم نشر خبر جديد',
      message: 'تم نشر خبر "اجتماع اللجنة البرلمانية" بنجاح',
      time: 'منذ ساعة',
      unread: true
    },
    {
      id: 3,
      title: 'تحديث مشروع',
      message: 'تم تحديث حالة مشروع "تطوير البنية التحتية"',
      time: 'منذ 3 ساعات',
      unread: false
    }
  ]

  const unreadCount = notifications.filter(n => n.unread).length

  const handleLogout = () => {
    logout()
  }

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* الجانب الأيسر - البحث */}
        <div className="flex items-center space-x-4 space-x-reverse flex-1">
          <div className="relative max-w-md w-full">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="البحث في لوحة التحكم..."
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent text-sm"
            />
          </div>
        </div>

        {/* الجانب الأيمن - الإشعارات والمستخدم */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* زر العودة للموقع */}
          <Link to="/">
            <Button variant="ghost" size="sm" className="text-gray-600 hover:text-iraqi-blue">
              <Home className="w-5 h-5" />
              <span className="mr-2 hidden sm:inline">عرض الموقع</span>
            </Button>
          </Link>

          {/* الإشعارات */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 text-gray-600 hover:text-iraqi-blue hover:bg-gray-100 rounded-lg transition-colors"
            >
              <Bell className="w-5 h-5" />
              {unreadCount > 0 && (
                <Badge 
                  variant="danger" 
                  className="absolute -top-1 -left-1 min-w-[20px] h-5 text-xs flex items-center justify-center"
                >
                  {unreadCount}
                </Badge>
              )}
            </button>

            {/* قائمة الإشعارات */}
            {showNotifications && (
              <div className="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="p-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-gray-900">الإشعارات</h3>
                    {unreadCount > 0 && (
                      <Badge variant="primary">{unreadCount} جديد</Badge>
                    )}
                  </div>
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${
                        notification.unread ? 'bg-blue-50' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3 space-x-reverse">
                        <div className={`w-2 h-2 rounded-full mt-2 ${
                          notification.unread ? 'bg-iraqi-blue' : 'bg-gray-300'
                        }`} />
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900">
                            {notification.title}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">
                            {notification.message}
                          </p>
                          <p className="text-xs text-gray-500 mt-2">
                            {notification.time}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="p-4 border-t border-gray-200">
                  <Button variant="ghost" size="sm" className="w-full text-iraqi-blue">
                    عرض جميع الإشعارات
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* قائمة المستخدم */}
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 space-x-reverse p-2 text-gray-600 hover:text-iraqi-blue hover:bg-gray-100 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 bg-iraqi-gold rounded-full flex items-center justify-center">
                <User className="w-4 h-4 text-white" />
              </div>
              <div className="hidden sm:block text-right">
                <p className="text-sm font-medium text-gray-900">
                  {user?.name || 'مدير النظام'}
                </p>
                <p className="text-xs text-gray-500">
                  {user?.role || 'مدير'}
                </p>
              </div>
              <ChevronDown className="w-4 h-4" />
            </button>

            {/* قائمة المستخدم المنسدلة */}
            {showUserMenu && (
              <div className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                <div className="p-2">
                  <Link
                    to="/admin/profile"
                    className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg"
                    onClick={() => setShowUserMenu(false)}
                  >
                    <User className="w-4 h-4" />
                    <span>الملف الشخصي</span>
                  </Link>
                  <Link
                    to="/admin/settings"
                    className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg"
                    onClick={() => setShowUserMenu(false)}
                  >
                    <Settings className="w-4 h-4" />
                    <span>الإعدادات</span>
                  </Link>
                  <hr className="my-2" />
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-2 space-x-reverse px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg w-full text-right"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>تسجيل الخروج</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}

export default AdminHeader
