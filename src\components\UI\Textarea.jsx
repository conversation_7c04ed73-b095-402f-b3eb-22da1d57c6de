import React, { forwardRef } from 'react'
import { AlertCircle } from 'lucide-react'

const Textarea = forwardRef(({
  label,
  placeholder,
  error,
  helperText,
  required = false,
  disabled = false,
  rows = 4,
  resize = 'vertical',
  size = 'md',
  variant = 'default',
  className = '',
  containerClassName = '',
  showCharCount = false,
  maxLength,
  ...props
}, ref) => {
  const [charCount, setCharCount] = React.useState(0)
  
  const baseClasses = 'w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variants = {
    default: 'border-gray-300 focus:border-iraqi-blue focus:ring-iraqi-blue',
    error: 'border-red-500 focus:border-red-500 focus:ring-red-500',
    success: 'border-green-500 focus:border-green-500 focus:ring-green-500'
  }
  
  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-base',
    lg: 'px-5 py-3 text-lg'
  }
  
  const resizeClasses = {
    none: 'resize-none',
    vertical: 'resize-y',
    horizontal: 'resize-x',
    both: 'resize'
  }
  
  const currentVariant = error ? 'error' : variant
  const classes = `${baseClasses} ${variants[currentVariant]} ${sizes[size]} ${resizeClasses[resize]} ${className}`
  
  const handleChange = (e) => {
    if (showCharCount || maxLength) {
      setCharCount(e.target.value.length)
    }
    if (props.onChange) {
      props.onChange(e)
    }
  }
  
  return (
    <div className={`space-y-2 ${containerClassName}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <textarea
          ref={ref}
          rows={rows}
          placeholder={placeholder}
          disabled={disabled}
          maxLength={maxLength}
          className={classes}
          onChange={handleChange}
          {...props}
        />
        
        {/* عداد الأحرف */}
        {(showCharCount || maxLength) && (
          <div className="absolute bottom-2 left-2 text-xs text-gray-500">
            {charCount}{maxLength && `/${maxLength}`}
          </div>
        )}
      </div>
      
      {/* رسالة الخطأ أو النص المساعد */}
      {error && (
        <p className="text-sm text-red-600 flex items-center">
          <AlertCircle className="w-4 h-4 ml-1" />
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="text-sm text-gray-500">{helperText}</p>
      )}
    </div>
  )
})

Textarea.displayName = 'Textarea'

export default Textarea
