import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import {
  Users,
  Calendar,
  Award,
  TrendingUp,
  ArrowLeft,
  Star,
  CheckCircle,
  Clock,
  Play,
  Eye,
  Heart,
  MessageSquare,
  Share2,
  Download,
  ExternalLink,
  ChevronLeft,
  ChevronRight,
  Quote,
  MapPin,
  Phone,
  Mail
} from 'lucide-react'

const Home = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isVisible, setIsVisible] = useState({})

  // إحصائيات محدثة ومفصلة
  const stats = [
    {
      label: 'سنوات الخدمة',
      value: '15+',
      icon: Calendar,
      color: 'text-iraqi-blue',
      description: 'سنة في خدمة الشعب العراقي',
      bgColor: 'bg-blue-50'
    },
    {
      label: 'المشاريع المنجزة',
      value: '120+',
      icon: CheckCircle,
      color: 'text-iraqi-green',
      description: 'مشروع تنموي مكتمل',
      bgColor: 'bg-green-50'
    },
    {
      label: 'المواطنون المستفيدون',
      value: '50,000+',
      icon: Users,
      color: 'text-iraqi-gold',
      description: 'مواطن استفاد من الخدمات',
      bgColor: 'bg-yellow-50'
    },
    {
      label: 'الجوائز والتكريمات',
      value: '25+',
      icon: Award,
      color: 'text-iraqi-red',
      description: 'جائزة وتكريم محلي ودولي',
      bgColor: 'bg-red-50'
    }
  ]

  // شرائح البطل المتحركة
  const heroSlides = [
    {
      id: 1,
      title: 'خدمة المواطن العراقي أولوية قصوى',
      subtitle: 'نعمل بجد لتحقيق تطلعات الشعب العراقي وبناء مستقبل أفضل',
      image: '/images/hero/slide1.jpg',
      cta: 'تعرف على إنجازاتنا',
      link: '/projects'
    },
    {
      id: 2,
      title: 'شفافية كاملة في العمل النيابي',
      subtitle: 'نؤمن بحق المواطن في معرفة كل تفاصيل العمل النيابي والمشاريع',
      image: '/images/hero/slide2.jpg',
      cta: 'اطلع على الأنشطة',
      link: '/news'
    },
    {
      id: 3,
      title: 'تواصل مستمر مع الناخبين',
      subtitle: 'أبوابنا مفتوحة دائماً لاستقبال آرائكم ومقترحاتكم',
      image: '/images/hero/slide3.jpg',
      cta: 'تواصل معنا',
      link: '/contact'
    }
  ]

  // أحدث الأخبار مع تفاصيل أكثر
  const latestNews = [
    {
      id: 1,
      title: 'افتتاح مشروع تطوير شبكة المياه في منطقة الكرادة',
      category: 'مشاريع',
      date: '15 يناير 2025',
      excerpt: 'تم افتتاح مشروع تطوير شبكة المياه الذي يخدم أكثر من 10,000 عائلة في منطقة الكرادة بتكلفة 5 مليارات دينار عراقي...',
      image: '/images/news/water-project.jpg',
      views: 1250,
      likes: 89,
      comments: 23
    },
    {
      id: 2,
      title: 'جلسة استماع مع المواطنين حول قانون الضمان الاجتماعي',
      category: 'فعاليات',
      date: '12 يناير 2025',
      excerpt: 'عقدت جلسة استماع مفتوحة مع المواطنين لمناقشة مشروع قانون الضمان الاجتماعي الجديد وتلقي المقترحات...',
      image: '/images/news/social-security.jpg',
      views: 980,
      likes: 67,
      comments: 34
    },
    {
      id: 3,
      title: 'زيارة ميدانية لمدارس بغداد لمتابعة سير العملية التعليمية',
      category: 'زيارات',
      date: '10 يناير 2025',
      excerpt: 'قام النائب بزيارة ميدانية لعدد من المدارس في بغداد للاطلاع على احتياجات الطلاب والمعلمين والبنية التحتية...',
      image: '/images/news/school-visit.jpg',
      views: 756,
      likes: 45,
      comments: 12
    },
    {
      id: 4,
      title: 'توقيع اتفاقية شراكة مع منظمات المجتمع المدني',
      category: 'شراكات',
      date: '8 يناير 2025',
      excerpt: 'تم توقيع اتفاقية شراكة استراتيجية مع عدة منظمات مجتمع مدني لتعزيز دور المواطن في التنمية المحلية...',
      image: '/images/news/partnership.jpg',
      views: 634,
      likes: 52,
      comments: 18
    }
  ]

  // المشاريع المميزة مع تفاصيل أكثر
  const featuredProjects = [
    {
      id: 1,
      title: 'مشروع تطوير البنية التحتية في بغداد',
      description: 'مشروع شامل لتطوير البنية التحتية يشمل الطرق والجسور وشبكات المياه والصرف الصحي',
      progress: 85,
      status: 'قيد التنفيذ',
      budget: '50 مليون دولار',
      beneficiaries: '200,000 مواطن',
      startDate: '2023-01-15',
      expectedCompletion: '2025-06-30',
      location: 'بغداد - مناطق متعددة',
      image: '/images/projects/infrastructure.jpg'
    },
    {
      id: 2,
      title: 'برنامج دعم الأسر المحتاجة',
      description: 'برنامج اجتماعي شامل لدعم الأسر ذات الدخل المحدود وتوفير فرص العمل والتدريب',
      progress: 100,
      status: 'مكتمل',
      budget: '25 مليون دولار',
      beneficiaries: '15,000 أسرة',
      startDate: '2022-03-01',
      expectedCompletion: '2024-12-31',
      location: 'بغداد والمحافظات',
      image: '/images/projects/social-support.jpg'
    },
    {
      id: 3,
      title: 'مشروع تطوير التعليم الرقمي',
      description: 'مبادرة لتطوير التعليم الرقمي في المدارس وتوفير الأجهزة والتدريب للمعلمين',
      progress: 60,
      status: 'قيد التنفيذ',
      budget: '15 مليون دولار',
      beneficiaries: '50,000 طالب',
      startDate: '2024-01-01',
      expectedCompletion: '2025-12-31',
      location: 'بغداد - 150 مدرسة',
      image: '/images/projects/digital-education.jpg'
    }
  ]

  // شهادات المواطنين
  const testimonials = [
    {
      id: 1,
      name: 'أحمد محمد علي',
      role: 'مواطن من منطقة الكرادة',
      content: 'بفضل جهود النائب تم حل مشكلة المياه في منطقتنا بعد سنوات من المعاناة. نشكره على اهتمامه المستمر بقضايا المواطنين.',
      rating: 5,
      image: '/images/testimonials/citizen1.jpg'
    },
    {
      id: 2,
      name: 'فاطمة حسن محمود',
      role: 'معلمة في مدرسة الزهراء',
      content: 'مشروع التعليم الرقمي غير حياة طلابنا للأفضل. الآن لدينا أجهزة حديثة وتدريب ممتاز للمعلمين.',
      rating: 5,
      image: '/images/testimonials/teacher1.jpg'
    },
    {
      id: 3,
      name: 'محمد عبد الله',
      role: 'صاحب مشروع صغير',
      content: 'استفدت من برنامج دعم المشاريع الصغيرة وتمكنت من توسيع عملي وتوظيف عمال جدد. شكراً لهذا الدعم.',
      rating: 5,
      image: '/images/testimonials/businessman1.jpg'
    }
  ]

  // خدمات سريعة
  const quickServices = [
    {
      id: 1,
      title: 'تقديم شكوى',
      description: 'قدم شكواك أو اقتراحك بسهولة',
      icon: MessageSquare,
      link: '/contact',
      color: 'bg-iraqi-blue'
    },
    {
      id: 2,
      title: 'طلب خدمة',
      description: 'اطلب الخدمات الحكومية المختلفة',
      icon: Users,
      link: '/support',
      color: 'bg-iraqi-green'
    },
    {
      id: 3,
      title: 'تحميل النماذج',
      description: 'حمل النماذج الرسمية المطلوبة',
      icon: Download,
      link: '/support',
      color: 'bg-iraqi-gold'
    },
    {
      id: 4,
      title: 'حجز موعد',
      description: 'احجز موعد للقاء النائب',
      icon: Calendar,
      link: '/contact',
      color: 'bg-iraqi-red'
    }
  ]

  // تأثير تغيير الشرائح التلقائي
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [heroSlides.length])

  // تأثير الظهور عند التمرير
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(prev => ({
              ...prev,
              [entry.target.id]: true
            }))
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = document.querySelectorAll('[data-animate]')
    elements.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <div className="min-h-screen">
      {/* قسم البطل المحسن مع الشرائح */}
      <section className="relative h-screen overflow-hidden">
        {/* الشرائح */}
        <div className="relative h-full">
          {heroSlides.map((slide, index) => (
            <div
              key={slide.id}
              className={`absolute inset-0 transition-opacity duration-1000 ${
                index === currentSlide ? 'opacity-100' : 'opacity-0'
              }`}
            >
              {/* خلفية الشريحة */}
              <div className="absolute inset-0 bg-gradient-to-r from-iraqi-blue via-blue-800 to-iraqi-blue">
                <div className="absolute inset-0 bg-black opacity-40"></div>
                <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60"></div>
              </div>

              {/* محتوى الشريحة */}
              <div className="container-custom relative z-10 h-full flex items-center">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center w-full">
                  <div className="space-y-8 text-white">
                    <div className="space-y-4">
                      <h1 className="text-5xl md:text-6xl font-bold leading-tight animate-slide-up">
                        {slide.title}
                      </h1>
                      <p className="text-xl md:text-2xl text-blue-100 leading-relaxed animate-fade-in">
                        {slide.subtitle}
                      </p>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 animate-slide-up">
                      <Link
                        to={slide.link}
                        className="btn-primary inline-flex items-center justify-center text-lg px-8 py-4"
                      >
                        {slide.cta}
                        <ArrowLeft className="w-5 h-5 mr-2" />
                      </Link>
                      <Link
                        to="/contact"
                        className="btn-outline bg-transparent border-white text-white hover:bg-white hover:text-iraqi-blue text-lg px-8 py-4"
                      >
                        تواصل معنا
                      </Link>
                    </div>
                  </div>

                  {/* صورة النائب */}
                  <div className="relative animate-fade-in">
                    <div className="w-full h-96 lg:h-[500px] bg-white bg-opacity-10 rounded-2xl backdrop-blur-sm flex items-center justify-center border border-white border-opacity-20">
                      <div className="text-center text-white">
                        <div className="w-32 h-32 bg-iraqi-gold rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl">
                          <span className="text-white text-4xl font-bold">ن</span>
                        </div>
                        <h3 className="text-2xl font-bold mb-2">النائب العراقي</h3>
                        <p className="text-lg text-blue-100">خدمة المواطن أولاً</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* مؤشرات الشرائح */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
          <div className="flex space-x-2 space-x-reverse">
            {heroSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-iraqi-gold scale-125'
                    : 'bg-white bg-opacity-50 hover:bg-opacity-75'
                }`}
              />
            ))}
          </div>
        </div>

        {/* أزرار التنقل */}
        <button
          onClick={() => setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length)}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
        <button
          onClick={() => setCurrentSlide((prev) => (prev + 1) % heroSlides.length)}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      </section>

      {/* الإحصائيات المحسنة */}
      <section className="py-20 bg-gray-50" data-animate id="stats">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">إنجازاتنا بالأرقام</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              نفخر بما حققناه من إنجازات في خدمة المواطن العراقي على مدى سنوات من العمل المتواصل
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div
                  key={index}
                  className={`text-center p-8 rounded-2xl ${stat.bgColor} hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100`}
                >
                  <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full bg-white shadow-lg mb-6`}>
                    <Icon className={`w-10 h-10 ${stat.color}`} />
                  </div>
                  <h3 className="text-4xl font-bold text-gray-900 mb-2">{stat.value}</h3>
                  <p className="text-lg font-semibold text-gray-800 mb-2">{stat.label}</p>
                  <p className="text-sm text-gray-600">{stat.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* قسم الخدمات السريعة */}
      <section className="py-20 bg-white" data-animate id="quick-services">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">خدمات سريعة</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              احصل على الخدمات التي تحتاجها بسهولة وسرعة من خلال منصتنا الرقمية
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {quickServices.map((service) => {
              const Icon = service.icon
              return (
                <Link
                  key={service.id}
                  to={service.link}
                  className="group block p-8 rounded-2xl bg-white border-2 border-gray-100 hover:border-iraqi-blue transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl"
                >
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-xl ${service.color} text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-iraqi-blue transition-colors duration-300">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                    {service.description}
                  </p>
                  <div className="mt-4 flex items-center text-iraqi-blue group-hover:translate-x-1 transition-transform duration-300">
                    <span className="text-sm font-medium">ابدأ الآن</span>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* أحدث الأخبار المحسنة */}
      <section className="py-20 bg-gray-50" data-animate id="latest-news">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">أحدث الأخبار والأنشطة</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              تابع آخر الأخبار والفعاليات والأنشطة النيابية بشفافية كاملة ومعلومات محدثة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 mb-8">
            {/* الخبر الرئيسي */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="relative">
                  <div className="w-full h-64 bg-gradient-to-r from-iraqi-blue to-blue-600 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Play className="w-8 h-8" />
                      </div>
                      <p className="text-lg">صورة الخبر الرئيسي</p>
                    </div>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="bg-iraqi-red text-white px-3 py-1 rounded-full text-sm font-medium">
                      عاجل
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="bg-iraqi-blue text-white px-3 py-1 rounded-full text-sm">
                      {latestNews[0].category}
                    </span>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Clock className="w-4 h-4 ml-1" />
                      {latestNews[0].date}
                    </div>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {latestNews[0].title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {latestNews[0].excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                      <div className="flex items-center">
                        <Eye className="w-4 h-4 ml-1" />
                        {latestNews[0].views}
                      </div>
                      <div className="flex items-center">
                        <Heart className="w-4 h-4 ml-1" />
                        {latestNews[0].likes}
                      </div>
                      <div className="flex items-center">
                        <MessageSquare className="w-4 h-4 ml-1" />
                        {latestNews[0].comments}
                      </div>
                    </div>
                    <Link
                      to={`/news/${latestNews[0].id}`}
                      className="inline-flex items-center text-iraqi-blue hover:text-blue-700 font-medium"
                    >
                      اقرأ المزيد
                      <ArrowLeft className="w-4 h-4 mr-1" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* الأخبار الفرعية */}
            <div className="space-y-6">
              {latestNews.slice(1, 4).map((news) => (
                <div key={news.id} className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                  <div className="flex gap-4">
                    <div className="w-20 h-20 bg-gradient-to-r from-iraqi-gold to-yellow-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <div className="text-center text-white">
                        <Calendar className="w-6 h-6 mx-auto mb-1" />
                        <span className="text-xs font-bold">{news.date.split(' ')[0]}</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                          {news.category}
                        </span>
                        <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                          <span className="flex items-center">
                            <Eye className="w-3 h-3 ml-1" />
                            {news.views}
                          </span>
                          <span className="flex items-center">
                            <Heart className="w-3 h-3 ml-1" />
                            {news.likes}
                          </span>
                        </div>
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2 text-sm">
                        {news.title}
                      </h4>
                      <p className="text-gray-600 text-xs line-clamp-2 mb-2">
                        {news.excerpt}
                      </p>
                      <Link
                        to={`/news/${news.id}`}
                        className="inline-flex items-center text-iraqi-blue hover:text-blue-700 font-medium text-xs"
                      >
                        اقرأ المزيد
                        <ArrowLeft className="w-3 h-3 mr-1" />
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="text-center">
            <Link to="/news" className="btn-primary text-lg px-8 py-4">
              عرض جميع الأخبار
              <ExternalLink className="w-5 h-5 mr-2" />
            </Link>
          </div>
        </div>
      </section>

      {/* المشاريع المميزة المحسنة */}
      <section className="py-20 bg-white" data-animate id="featured-projects">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">المشاريع المميزة</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              تعرف على أهم المشاريع والإنجازات التي تم تحقيقها لخدمة المواطن العراقي
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {featuredProjects.map((project, index) => (
              <div key={project.id} className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                {/* صورة المشروع */}
                <div className="relative h-48 bg-gradient-to-r from-iraqi-blue to-blue-600 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8" />
                    </div>
                    <p className="text-sm">صورة المشروع</p>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      project.status === 'مكتمل'
                        ? 'bg-green-500 text-white'
                        : project.status === 'قيد التنفيذ'
                        ? 'bg-blue-500 text-white'
                        : 'bg-yellow-500 text-white'
                    }`}>
                      {project.status}
                    </span>
                  </div>
                </div>

                {/* محتوى المشروع */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {project.description}
                  </p>

                  {/* معلومات المشروع */}
                  <div className="space-y-4 mb-6">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">التقدم</span>
                      <span className="font-semibold text-gray-900">{project.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className={`h-3 rounded-full transition-all duration-500 ${
                          project.progress === 100 ? 'bg-green-500' : 'bg-iraqi-blue'
                        }`}
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500 block">الميزانية</span>
                        <span className="font-semibold text-iraqi-blue">{project.budget}</span>
                      </div>
                      <div>
                        <span className="text-gray-500 block">المستفيدون</span>
                        <span className="font-semibold text-iraqi-green">{project.beneficiaries}</span>
                      </div>
                    </div>

                    <div className="text-sm">
                      <div className="flex items-center text-gray-500 mb-1">
                        <MapPin className="w-4 h-4 ml-1" />
                        {project.location}
                      </div>
                      <div className="flex items-center text-gray-500">
                        <Calendar className="w-4 h-4 ml-1" />
                        {project.startDate} - {project.expectedCompletion}
                      </div>
                    </div>
                  </div>

                  <Link
                    to={`/projects/${project.id}`}
                    className="w-full btn-primary text-center block"
                  >
                    عرض التفاصيل
                    <ExternalLink className="w-4 h-4 mr-2" />
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link to="/projects" className="btn-primary text-lg px-8 py-4">
              عرض جميع المشاريع
              <ArrowLeft className="w-5 h-5 mr-2" />
            </Link>
          </div>
        </div>
      </section>

      {/* شهادات المواطنين */}
      <section className="py-20 bg-gray-50" data-animate id="testimonials">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">شهادات المواطنين</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              استمع إلى آراء وشهادات المواطنين حول الخدمات والمشاريع المقدمة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                <div className="flex items-center mb-6">
                  <Quote className="w-8 h-8 text-iraqi-gold ml-3" />
                  <div className="flex text-iraqi-gold">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <span key={i} className="text-xl">★</span>
                    ))}
                  </div>
                </div>

                <p className="text-gray-700 mb-6 leading-relaxed text-lg">
                  "{testimonial.content}"
                </p>

                <div className="flex items-center">
                  <div className="w-12 h-12 bg-iraqi-blue rounded-full flex items-center justify-center text-white font-bold text-lg ml-4">
                    {testimonial.name.charAt(0)}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                    <p className="text-gray-500 text-sm">{testimonial.location}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* دعوة للتواصل المحسنة */}
      <section className="py-20 bg-gradient-to-r from-iraqi-blue via-blue-800 to-iraqi-blue text-white relative overflow-hidden" data-animate id="contact-cta">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-10 right-10 w-32 h-32 bg-iraqi-gold opacity-10 rounded-full"></div>
          <div className="absolute bottom-10 left-10 w-24 h-24 bg-iraqi-green opacity-10 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white opacity-5 rounded-full"></div>
        </div>

        <div className="container-custom text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">هل تحتاج إلى مساعدة؟</h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-12 leading-relaxed">
              نحن هنا لخدمتكم على مدار الساعة. تواصلوا معنا لأي استفسار أو طلب خدمة أو اقتراح
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="text-center">
                <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Phone className="w-8 h-8" />
                </div>
                <h3 className="text-lg font-semibold mb-2">اتصل بنا</h3>
                <p className="text-blue-100">متاح 24/7</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Mail className="w-8 h-8" />
                </div>
                <h3 className="text-lg font-semibold mb-2">راسلنا</h3>
                <p className="text-blue-100">رد سريع مضمون</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="w-8 h-8" />
                </div>
                <h3 className="text-lg font-semibold mb-2">زورنا</h3>
                <p className="text-blue-100">مكتب مفتوح للجميع</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/support"
                className="btn-secondary text-lg px-8 py-4 transform hover:scale-105 transition-all duration-300"
              >
                طلب خدمة
                <ArrowLeft className="w-5 h-5 mr-2" />
              </Link>
              <Link
                to="/contact"
                className="btn-outline bg-transparent border-2 border-white text-white hover:bg-white hover:text-iraqi-blue text-lg px-8 py-4 transform hover:scale-105 transition-all duration-300"
              >
                تواصل معنا
                <Mail className="w-5 h-5 mr-2" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
