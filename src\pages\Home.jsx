import React from 'react'
import { Link } from 'react-router-dom'
import { 
  Users, 
  Calendar, 
  Award, 
  TrendingUp,
  ArrowLeft,
  Star,
  CheckCircle,
  Clock
} from 'lucide-react'

const Home = () => {
  const stats = [
    { label: 'سنوات الخدمة', value: '15+', icon: Calendar, color: 'text-iraqi-blue' },
    { label: 'المشاريع المنجزة', value: '120+', icon: CheckCircle, color: 'text-iraqi-green' },
    { label: 'المواطنون المستفيدون', value: '50,000+', icon: Users, color: 'text-iraqi-gold' },
    { label: 'الجوائز والتكريمات', value: '25+', icon: Award, color: 'text-iraqi-red' }
  ]

  const latestNews = [
    {
      id: 1,
      title: 'مناقشة قانون الضمان الاجتماعي الجديد',
      excerpt: 'تم مناقشة مشروع قانون الضمان الاجتماعي الجديد في جلسة البرلمان اليوم...',
      date: '2025-01-15',
      image: '/images/news1.jpg',
      category: 'قوانين'
    },
    {
      id: 2,
      title: 'زيارة ميدانية لمشروع الإسكان الحكومي',
      excerpt: 'قام النائب بزيارة ميدانية لمتابعة تقدم مشروع الإسكان الحكومي...',
      date: '2025-01-12',
      image: '/images/news2.jpg',
      category: 'زيارات'
    },
    {
      id: 3,
      title: 'اجتماع لجنة الخدمات والإعمار',
      excerpt: 'عُقد اجتماع لجنة الخدمات والإعمار لمناقشة المشاريع الجديدة...',
      date: '2025-01-10',
      image: '/images/news3.jpg',
      category: 'اجتماعات'
    }
  ]

  const featuredProjects = [
    {
      id: 1,
      title: 'مشروع تطوير البنية التحتية',
      description: 'مشروع شامل لتطوير البنية التحتية في المحافظة',
      progress: 85,
      status: 'قيد التنفيذ',
      budget: '50 مليون دولار'
    },
    {
      id: 2,
      title: 'برنامج دعم الأسر المحتاجة',
      description: 'برنامج اجتماعي لدعم الأسر ذات الدخل المحدود',
      progress: 100,
      status: 'مكتمل',
      budget: '25 مليون دولار'
    }
  ]

  return (
    <div className="min-h-screen">
      {/* قسم البطل */}
      <section className="relative bg-gradient-to-r from-iraqi-blue to-blue-800 text-white py-20">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="container-custom relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl font-bold leading-tight">
                مرحباً بكم في موقع
                <span className="text-iraqi-gold block">النائب العراقي الرسمي</span>
              </h1>
              <p className="text-xl text-blue-100 leading-relaxed">
                منصة رقمية شاملة لخدمة المواطنين العراقيين وعرض أنشطة النائب بشفافية كاملة
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/support"
                  className="btn-secondary inline-flex items-center justify-center"
                >
                  طلب خدمة
                  <ArrowLeft className="w-5 h-5 mr-2" />
                </Link>
                <Link
                  to="/contact"
                  className="btn-outline bg-transparent border-white text-white hover:bg-white hover:text-iraqi-blue"
                >
                  تواصل معنا
                </Link>
              </div>
            </div>
            <div className="flex justify-center">
              <div className="w-80 h-80 bg-white rounded-full shadow-2xl flex items-center justify-center">
                <div className="w-72 h-72 bg-iraqi-gold rounded-full flex items-center justify-center">
                  <span className="text-white text-8xl font-bold">ن</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* الإحصائيات */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className="text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-200">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 mb-4`}>
                    <Icon className={`w-8 h-8 ${stat.color}`} />
                  </div>
                  <h3 className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</h3>
                  <p className="text-gray-600">{stat.label}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* آخر الأخبار */}
      <section className="py-16 bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">آخر الأخبار والأنشطة</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              تابع أحدث الأنشطة والفعاليات والقرارات المهمة
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {latestNews.map((news) => (
              <div key={news.id} className="card hover:shadow-xl transition-shadow duration-300">
                <div className="aspect-w-16 aspect-h-9 mb-4">
                  <div className="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-500">صورة الخبر</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="bg-iraqi-blue text-white px-3 py-1 rounded-full text-sm">
                      {news.category}
                    </span>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Clock className="w-4 h-4 ml-1" />
                      {news.date}
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                    {news.title}
                  </h3>
                  <p className="text-gray-600 text-sm line-clamp-3">
                    {news.excerpt}
                  </p>
                  <Link
                    to={`/news/${news.id}`}
                    className="text-iraqi-blue hover:text-blue-700 font-medium text-sm inline-flex items-center"
                  >
                    اقرأ المزيد
                    <ArrowLeft className="w-4 h-4 mr-1" />
                  </Link>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link to="/news" className="btn-primary">
              عرض جميع الأخبار
            </Link>
          </div>
        </div>
      </section>

      {/* المشاريع المميزة */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">المشاريع المميزة</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              تعرف على أهم المشاريع والإنجازات التي تم تحقيقها
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {featuredProjects.map((project) => (
              <div key={project.id} className="card">
                <div className="flex items-start justify-between mb-4">
                  <h3 className="text-xl font-semibold text-gray-900">{project.title}</h3>
                  <span className={`px-3 py-1 rounded-full text-sm ${
                    project.status === 'مكتمل' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {project.status}
                  </span>
                </div>
                <p className="text-gray-600 mb-4">{project.description}</p>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>التقدم</span>
                    <span>{project.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-iraqi-green h-2 rounded-full transition-all duration-300"
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center pt-2">
                    <span className="text-sm text-gray-500">الميزانية: {project.budget}</span>
                    <Link
                      to={`/projects/${project.id}`}
                      className="text-iraqi-blue hover:text-blue-700 font-medium text-sm"
                    >
                      التفاصيل
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link to="/projects" className="btn-primary">
              عرض جميع المشاريع
            </Link>
          </div>
        </div>
      </section>

      {/* دعوة للتواصل */}
      <section className="py-16 bg-iraqi-blue text-white">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold mb-4">هل تحتاج إلى مساعدة؟</h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            نحن هنا لخدمتكم. تواصلوا معنا لأي استفسار أو طلب خدمة
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/support" className="btn-secondary">
              طلب خدمة
            </Link>
            <Link to="/contact" className="btn-outline bg-transparent border-white text-white hover:bg-white hover:text-iraqi-blue">
              تواصل معنا
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
