import React, { useState } from 'react'
import { 
  Search, 
  Filter, 
  Calendar, 
  Clock, 
  Eye,
  ArrowLeft,
  Tag
} from 'lucide-react'

const News = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('الكل')
  const [sortBy, setSortBy] = useState('الأحدث')

  const categories = [
    'الكل',
    'قوانين',
    'زيارات',
    'اجتماعات',
    'بيانات',
    'فعاليات',
    'مؤتمرات'
  ]

  const sortOptions = [
    'الأحدث',
    'الأقدم',
    'الأكثر مشاهدة'
  ]

  const newsData = [
    {
      id: 1,
      title: 'مناقشة قانون الضمان الاجتماعي الجديد في البرلمان',
      excerpt: 'تم مناقشة مشروع قانون الضمان الاجتماعي الجديد في جلسة البرلمان اليوم، والذي يهدف إلى تحسين أوضاع المواطنين ذوي الدخل المحدود...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-15',
      time: '14:30',
      category: 'قوانين',
      views: 1250,
      image: '/images/news1.jpg',
      featured: true
    },
    {
      id: 2,
      title: 'زيارة ميدانية لمشروع الإسكان الحكومي في منطقة الكرادة',
      excerpt: 'قام النائب بزيارة ميدانية لمتابعة تقدم مشروع الإسكان الحكومي في منطقة الكرادة، والاطلاع على سير العمل والتحديات...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-12',
      time: '10:15',
      category: 'زيارات',
      views: 890,
      image: '/images/news2.jpg',
      featured: false
    },
    {
      id: 3,
      title: 'اجتماع لجنة الخدمات والإعمار لمناقشة المشاريع الجديدة',
      excerpt: 'عُقد اجتماع لجنة الخدمات والإعمار لمناقشة المشاريع الجديدة المقترحة لتطوير البنية التحتية في المحافظة...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-10',
      time: '09:00',
      category: 'اجتماعات',
      views: 675,
      image: '/images/news3.jpg',
      featured: false
    },
    {
      id: 4,
      title: 'بيان حول أزمة المياه في المحافظات الجنوبية',
      excerpt: 'أصدر النائب بياناً حول أزمة المياه في المحافظات الجنوبية، مطالباً بحلول عاجلة لمعالجة هذه المشكلة الحيوية...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-08',
      time: '16:45',
      category: 'بيانات',
      views: 1100,
      image: '/images/news4.jpg',
      featured: false
    },
    {
      id: 5,
      title: 'مشاركة في مؤتمر التنمية المستدامة في دبي',
      excerpt: 'شارك النائب في مؤتمر التنمية المستدامة المنعقد في دبي، حيث قدم ورقة عمل حول التحديات البيئية في العراق...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-05',
      time: '11:20',
      category: 'مؤتمرات',
      views: 520,
      image: '/images/news5.jpg',
      featured: false
    },
    {
      id: 6,
      title: 'فعالية توزيع المساعدات على الأسر المحتاجة',
      excerpt: 'نظم مكتب النائب فعالية لتوزيع المساعدات الغذائية والمالية على الأسر المحتاجة في المنطقة، بمشاركة منظمات المجتمع المدني...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-03',
      time: '13:00',
      category: 'فعاليات',
      views: 780,
      image: '/images/news6.jpg',
      featured: false
    }
  ]

  // تصفية الأخبار حسب البحث والفئة
  const filteredNews = newsData.filter(news => {
    const matchesSearch = news.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         news.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'الكل' || news.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // ترتيب الأخبار
  const sortedNews = [...filteredNews].sort((a, b) => {
    switch (sortBy) {
      case 'الأقدم':
        return new Date(a.date) - new Date(b.date)
      case 'الأكثر مشاهدة':
        return b.views - a.views
      default: // الأحدث
        return new Date(b.date) - new Date(a.date)
    }
  })

  const featuredNews = newsData.filter(news => news.featured)

  return (
    <div className="min-h-screen py-12">
      <div className="container-custom">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">الأخبار والأنشطة</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            تابع أحدث الأنشطة والفعاليات والقرارات المهمة للنائب العراقي
          </p>
        </div>

        {/* الأخبار المميزة */}
        {featuredNews.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">الأخبار المميزة</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredNews.map((news) => (
                <div key={news.id} className="card hover:shadow-xl transition-shadow duration-300">
                  <div className="aspect-w-16 aspect-h-9 mb-4">
                    <div className="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                      <span className="text-gray-500">صورة الخبر المميز</span>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="bg-iraqi-red text-white px-3 py-1 rounded-full text-sm font-medium">
                        مميز
                      </span>
                      <div className="flex items-center text-gray-500 text-sm">
                        <Eye className="w-4 h-4 ml-1" />
                        {news.views}
                      </div>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 line-clamp-2">
                      {news.title}
                    </h3>
                    <p className="text-gray-600 line-clamp-3">
                      {news.excerpt}
                    </p>
                    <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 ml-1" />
                          {news.date}
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 ml-1" />
                          {news.time}
                        </div>
                      </div>
                      <button className="text-iraqi-blue hover:text-blue-700 font-medium text-sm inline-flex items-center">
                        اقرأ المزيد
                        <ArrowLeft className="w-4 h-4 mr-1" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* أدوات البحث والتصفية */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="ابحث في الأخبار..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
              />
            </div>

            {/* تصفية حسب الفئة */}
            <div className="relative">
              <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent appearance-none"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* الترتيب */}
            <div>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
              >
                {sortOptions.map((option) => (
                  <option key={option} value={option}>
                    ترتيب حسب: {option}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* قائمة الأخبار */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sortedNews.map((news) => (
            <div key={news.id} className="card hover:shadow-xl transition-shadow duration-300">
              <div className="aspect-w-16 aspect-h-9 mb-4">
                <div className="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500">صورة الخبر</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="bg-iraqi-blue text-white px-3 py-1 rounded-full text-sm">
                    {news.category}
                  </span>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Eye className="w-4 h-4 ml-1" />
                    {news.views}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                  {news.title}
                </h3>
                <p className="text-gray-600 text-sm line-clamp-3">
                  {news.excerpt}
                </p>
                <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                  <div className="flex items-center space-x-3 space-x-reverse text-xs text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-3 h-3 ml-1" />
                      {news.date}
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-3 h-3 ml-1" />
                      {news.time}
                    </div>
                  </div>
                  <button className="text-iraqi-blue hover:text-blue-700 font-medium text-sm inline-flex items-center">
                    اقرأ المزيد
                    <ArrowLeft className="w-4 h-4 mr-1" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* رسالة عدم وجود نتائج */}
        {sortedNews.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد نتائج</h3>
            <p className="text-gray-600">
              لم يتم العثور على أخبار تطابق معايير البحث المحددة
            </p>
          </div>
        )}

        {/* إحصائيات الأخبار */}
        <div className="mt-12 bg-iraqi-blue text-white rounded-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <h3 className="text-3xl font-bold mb-2">{newsData.length}</h3>
              <p className="text-blue-100">إجمالي الأخبار</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold mb-2">{categories.length - 1}</h3>
              <p className="text-blue-100">الفئات المتاحة</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold mb-2">
                {newsData.reduce((sum, news) => sum + news.views, 0).toLocaleString()}
              </h3>
              <p className="text-blue-100">إجمالي المشاهدات</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default News
