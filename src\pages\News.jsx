import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import {
  Search,
  Filter,
  Calendar,
  Eye,
  Clock,
  Tag,
  TrendingUp,
  Share2,
  Bookmark,
  Heart,
  MessageSquare,
  ChevronLeft,
  ChevronRight,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Star,
  Play,
  Download,
  ExternalLink,
  User,
  MapPin,
  Globe
} from 'lucide-react'

const News = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [sortBy, setSortBy] = useState('newest')
  const [viewMode, setViewMode] = useState('grid') // grid or list
  const [currentPage, setCurrentPage] = useState(1)
  const [isVisible, setIsVisible] = useState({})
  const [selectedNews, setSelectedNews] = useState(null)
  const [showNewsModal, setShowNewsModal] = useState(false)
  const [bookmarkedNews, setBookmarkedNews] = useState([])
  const [likedNews, setLikedNews] = useState([])

  const newsPerPage = 9

  // إعداد Intersection Observer للتأثيرات الحركية
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(prev => ({ ...prev, [entry.target.id]: true }))
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = document.querySelectorAll('[data-animate]')
    elements.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  const categories = [
    { id: 'all', name: 'جميع الفئات', icon: Globe, color: 'bg-gray-500' },
    { id: 'laws', name: 'قوانين وتشريعات', icon: Tag, color: 'bg-iraqi-blue' },
    { id: 'visits', name: 'زيارات ميدانية', icon: MapPin, color: 'bg-iraqi-green' },
    { id: 'meetings', name: 'اجتماعات ولجان', icon: User, color: 'bg-iraqi-gold' },
    { id: 'statements', name: 'بيانات وتصريحات', icon: MessageSquare, color: 'bg-iraqi-red' },
    { id: 'events', name: 'فعاليات وأنشطة', icon: Star, color: 'bg-purple-500' },
    { id: 'conferences', name: 'مؤتمرات ومنتديات', icon: Globe, color: 'bg-indigo-500' }
  ]

  const sortOptions = [
    { value: 'newest', label: 'الأحدث', icon: SortDesc },
    { value: 'oldest', label: 'الأقدم', icon: SortAsc },
    { value: 'most_viewed', label: 'الأكثر مشاهدة', icon: Eye },
    { value: 'most_liked', label: 'الأكثر إعجاباً', icon: Heart },
    { value: 'alphabetical', label: 'أبجدياً', icon: SortAsc }
  ]

  const newsData = [
    {
      id: 1,
      title: 'إقرار قانون الضمان الاجتماعي الجديد بالقراءة الثانية',
      excerpt: 'صوت مجلس النواب العراقي بالإجماع على قانون الضمان الاجتماعي الجديد بالقراءة الثانية، والذي يشمل زيادة المخصصات وتوسيع نطاق الاستفادة لتشمل فئات جديدة من المجتمع.',
      content: `أقر مجلس النواب العراقي اليوم قانون الضمان الاجتماعي الجديد بالقراءة الثانية بإجماع الكتل السياسية، في خطوة مهمة نحو تحسين الأوضاع المعيشية للمواطنين ذوي الدخل المحدود.

      يتضمن القانون الجديد زيادة مخصصات الضمان الاجتماعي بنسبة 40% وتوسيع نطاق الاستفادة لتشمل الأرامل والأيتام وذوي الاحتياجات الخاصة والعاطلين عن العمل.

      وأكد النائب د. أحمد محمد العراقي، عضو لجنة العمل والشؤون الاجتماعية، أن هذا القانون يمثل نقلة نوعية في منظومة الحماية الاجتماعية في العراق.`,
      date: '2025-01-15',
      time: '14:30',
      category: 'laws',
      views: 2450,
      likes: 189,
      comments: 45,
      shares: 78,
      image: '/images/news1.jpg',
      featured: true,
      urgent: true,
      tags: ['قوانين', 'ضمان اجتماعي', 'مجلس النواب', 'حماية اجتماعية'],
      author: 'د. أحمد محمد العراقي',
      location: 'مجلس النواب العراقي - بغداد',
      readTime: '5 دقائق',
      type: 'article'
    },
    {
      id: 2,
      title: 'زيارة ميدانية شاملة لمشاريع الإسكان في الكرادة والجادرية',
      excerpt: 'قام النائب بجولة ميدانية واسعة شملت مشاريع الإسكان الحكومي في منطقتي الكرادة والجادرية، للوقوف على التقدم المحرز ومعالجة التحديات التي تواجه المشاريع.',
      content: `نفذ النائب د. أحمد محمد العراقي جولة ميدانية شاملة شملت مشاريع الإسكان الحكومي في منطقتي الكرادة والجادرية، بحضور مدير عام دائرة الإسكان ومهندسين مختصين.

      وخلال الزيارة، اطلع النائب على سير العمل في 450 وحدة سكنية قيد الإنشاء، وناقش مع المقاولين التحديات التي تواجه المشروع والحلول المقترحة لتسريع وتيرة الإنجاز.

      وأكد النائب على ضرورة الالتزام بالمواصفات الفنية والجودة العالية، مشدداً على أهمية تسليم الوحدات في المواعيد المحددة.`,
      date: '2025-01-12',
      time: '10:15',
      category: 'visits',
      views: 1890,
      likes: 156,
      comments: 32,
      shares: 67,
      image: '/images/news2.jpg',
      featured: false,
      urgent: false,
      tags: ['زيارات ميدانية', 'إسكان', 'كرادة', 'جادرية', 'مشاريع حكومية'],
      author: 'د. أحمد محمد العراقي',
      location: 'الكرادة والجادرية - بغداد',
      readTime: '4 دقائق',
      type: 'report'
    },
    {
      id: 3,
      title: 'اجتماع طارئ للجنة الخدمات لمناقشة أزمة الكهرباء',
      excerpt: 'عقدت لجنة الخدمات والإعمار اجتماعاً طارئاً لمناقشة أزمة انقطاع التيار الكهربائي في العاصمة بغداد والمحافظات، ووضع خطة عاجلة لمعالجة المشكلة.',
      content: `عقدت لجنة الخدمات والإعمار في مجلس النواب اجتماعاً طارئاً برئاسة النائب د. أحمد محمد العراقي لمناقشة أزمة انقطاع التيار الكهربائي التي تشهدها العاصمة بغداد وعدد من المحافظات.

      وخلال الاجتماع، استمعت اللجنة إلى تقرير مفصل من وزير الكهرباء حول أسباب الأزمة والخطوات المتخذة لمعالجتها، بما في ذلك صيانة المحطات وزيادة الاستيراد من الدول المجاورة.

      وطالبت اللجنة بوضع خطة زمنية واضحة لحل المشكلة خلال الأسابيع القادمة، مع ضرورة تعويض المواطنين المتضررين.`,
      date: '2025-01-10',
      time: '09:00',
      category: 'meetings',
      views: 1675,
      likes: 134,
      comments: 89,
      shares: 45,
      image: '/images/news3.jpg',
      featured: true,
      urgent: true,
      tags: ['اجتماعات', 'لجنة الخدمات', 'كهرباء', 'أزمة', 'حلول عاجلة'],
      author: 'د. أحمد محمد العراقي',
      location: 'مجلس النواب العراقي - بغداد',
      readTime: '6 دقائق',
      type: 'meeting'
    },
    {
      id: 4,
      title: 'بيان عاجل حول أزمة المياه في المحافظات الجنوبية',
      excerpt: 'أصدر النائب بياناً عاجلاً حول تفاقم أزمة المياه في محافظات البصرة وذي قار وميسان، مطالباً الحكومة باتخاذ إجراءات فورية لمعالجة هذه الأزمة الإنسانية.',
      content: `أصدر النائب د. أحمد محمد العراقي بياناً عاجلاً حول تفاقم أزمة المياه في المحافظات الجنوبية، خاصة البصرة وذي قار وميسان، والتي تشهد نقصاً حاداً في المياه الصالحة للشرب.

      وأكد البيان أن هذه الأزمة تمثل تهديداً مباشراً لحياة المواطنين وصحتهم، مطالباً الحكومة الاتحادية بإعلان حالة الطوارئ في هذه المحافظات وتخصيص ميزانية عاجلة لمعالجة المشكلة.

      ودعا النائب إلى تشكيل لجنة برلمانية عليا للتحقيق في أسباب الأزمة ومحاسبة المقصرين، مع وضع خطة استراتيجية طويلة المدى لضمان الأمن المائي للعراق.`,
      date: '2025-01-08',
      time: '16:45',
      category: 'statements',
      views: 3100,
      likes: 267,
      comments: 156,
      shares: 189,
      image: '/images/news4.jpg',
      featured: true,
      urgent: true,
      tags: ['بيانات', 'أزمة مياه', 'محافظات جنوبية', 'طوارئ', 'حلول عاجلة'],
      author: 'د. أحمد محمد العراقي',
      location: 'مكتب النائب - بغداد',
      readTime: '7 دقائق',
      type: 'statement'
    },
    {
      id: 5,
      title: 'مشاركة متميزة في مؤتمر التنمية المستدامة بدبي',
      excerpt: 'شارك النائب في مؤتمر التنمية المستدامة الدولي في دبي، حيث قدم ورقة عمل حول "التحديات البيئية في العراق والحلول المبتكرة"، لاقت استحساناً واسعاً من المشاركين.',
      content: `شارك النائب د. أحمد محمد العراقي في مؤتمر التنمية المستدامة الدولي المنعقد في دبي، والذي ضم خبراء ومسؤولين من أكثر من 50 دولة حول العالم.

      وقدم النائب ورقة عمل بعنوان "التحديات البيئية في العراق والحلول المبتكرة"، تناولت فيها قضايا التصحر ونقص المياه والتلوث البيئي، مع اقتراح حلول عملية مستدامة.

      وأشاد المشاركون في المؤتمر بالورقة المقدمة، مؤكدين على أهمية التعاون الدولي في مواجهة التحديات البيئية، وتم دعوة العراق للمشاركة في برامج التعاون البيئي الإقليمي.`,
      date: '2025-01-05',
      time: '11:20',
      category: 'conferences',
      views: 1520,
      likes: 98,
      comments: 23,
      shares: 67,
      image: '/images/news5.jpg',
      featured: false,
      urgent: false,
      tags: ['مؤتمرات', 'تنمية مستدامة', 'دبي', 'بيئة', 'تعاون دولي'],
      author: 'د. أحمد محمد العراقي',
      location: 'دبي - الإمارات العربية المتحدة',
      readTime: '5 دقائق',
      type: 'conference'
    },
    {
      id: 6,
      title: 'حملة إنسانية واسعة لتوزيع المساعدات على الأسر المحتاجة',
      excerpt: 'نظم مكتب النائب حملة إنسانية واسعة شملت توزيع 500 سلة غذائية ومساعدات مالية على الأسر المحتاجة في مناطق مختلفة من بغداد، بالتعاون مع منظمات المجتمع المدني.',
      content: `نظم مكتب النائب د. أحمد محمد العراقي حملة إنسانية واسعة شملت توزيع 500 سلة غذائية ومساعدات مالية على الأسر المحتاجة في مناطق الشعلة والحرية والكاظمية.

      وشارك في الحملة عدد من منظمات المجتمع المدني والمتطوعين، حيث تم توزيع المساعدات على الأرامل والأيتام وذوي الاحتياجات الخاصة والعائلات ذات الدخل المحدود.

      وأكد النائب أن هذه الحملة تأتي ضمن برنامج شامل للمسؤولية الاجتماعية، مشيراً إلى أن مكتبه يعمل على تنظيم حملات مماثلة بشكل دوري لدعم الفئات المحتاجة في المجتمع.`,
      date: '2025-01-03',
      time: '13:00',
      category: 'events',
      views: 1780,
      likes: 234,
      comments: 67,
      shares: 123,
      image: '/images/news6.jpg',
      featured: false,
      urgent: false,
      tags: ['فعاليات', 'مساعدات إنسانية', 'أسر محتاجة', 'مجتمع مدني', 'مسؤولية اجتماعية'],
      author: 'د. أحمد محمد العراقي',
      location: 'مناطق متفرقة - بغداد',
      readTime: '4 دقائق',
      type: 'event'
    }
  ]

  // إضافة المزيد من الأخبار للتنويع
  const additionalNews = [
    {
      id: 7,
      title: 'افتتاح مركز صحي جديد في منطقة الصدر',
      excerpt: 'تم افتتاح مركز صحي متطور في منطقة الصدر بحضور النائب ووزير الصحة، ليخدم أكثر من 50 ألف مواطن في المنطقة.',
      content: 'محتوى مفصل...',
      date: '2025-01-02',
      time: '11:00',
      category: 'events',
      views: 945,
      likes: 87,
      comments: 23,
      shares: 34,
      image: '/images/news7.jpg',
      featured: false,
      urgent: false,
      tags: ['صحة', 'افتتاح', 'مركز صحي', 'الصدر'],
      author: 'د. أحمد محمد العراقي',
      location: 'منطقة الصدر - بغداد',
      readTime: '3 دقائق',
      type: 'event'
    },
    {
      id: 8,
      title: 'مناقشة موازنة 2025 في جلسة برلمانية مفتوحة',
      excerpt: 'شارك النائب في جلسة برلمانية مفتوحة لمناقشة مشروع موازنة 2025، مؤكداً على أهمية زيادة المخصصات للتعليم والصحة.',
      content: 'محتوى مفصل...',
      date: '2024-12-28',
      time: '15:30',
      category: 'laws',
      views: 2100,
      likes: 178,
      comments: 92,
      shares: 156,
      image: '/images/news8.jpg',
      featured: false,
      urgent: false,
      tags: ['موازنة', '2025', 'برلمان', 'تعليم', 'صحة'],
      author: 'د. أحمد محمد العراقي',
      location: 'مجلس النواب العراقي - بغداد',
      readTime: '8 دقائق',
      type: 'article'
    }
  ]

  const allNews = [...newsData, ...additionalNews]

  // تصفية الأخبار حسب البحث والفئة
  const filteredNews = allNews.filter(news => {
    const matchesSearch = news.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         news.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         news.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === 'all' || !selectedCategory || news.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // ترتيب الأخبار
  const sortedNews = [...filteredNews].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.date) - new Date(b.date)
      case 'most_viewed':
        return b.views - a.views
      case 'most_liked':
        return b.likes - a.likes
      case 'alphabetical':
        return a.title.localeCompare(b.title, 'ar')
      default: // newest
        return new Date(b.date) - new Date(a.date)
    }
  })

  // تقسيم الأخبار للصفحات
  const totalPages = Math.ceil(sortedNews.length / newsPerPage)
  const startIndex = (currentPage - 1) * newsPerPage
  const paginatedNews = sortedNews.slice(startIndex, startIndex + newsPerPage)

  const featuredNews = allNews.filter(news => news.featured)
  const urgentNews = allNews.filter(news => news.urgent)

  // وظائف التفاعل
  const handleNewsClick = (news) => {
    setSelectedNews(news)
    setShowNewsModal(true)
    // زيادة عدد المشاهدات
    const newsIndex = allNews.findIndex(n => n.id === news.id)
    if (newsIndex !== -1) {
      allNews[newsIndex].views += 1
    }
  }

  const handleBookmark = (newsId) => {
    setBookmarkedNews(prev =>
      prev.includes(newsId)
        ? prev.filter(id => id !== newsId)
        : [...prev, newsId]
    )
  }

  const handleLike = (newsId) => {
    setLikedNews(prev =>
      prev.includes(newsId)
        ? prev.filter(id => id !== newsId)
        : [...prev, newsId]
    )

    // تحديث عدد الإعجابات
    const newsIndex = allNews.findIndex(n => n.id === newsId)
    if (newsIndex !== -1) {
      if (likedNews.includes(newsId)) {
        allNews[newsIndex].likes -= 1
      } else {
        allNews[newsIndex].likes += 1
      }
    }
  }

  const handleShare = (news) => {
    if (navigator.share) {
      navigator.share({
        title: news.title,
        text: news.excerpt,
        url: window.location.href
      })
    } else {
      // نسخ الرابط للحافظة
      navigator.clipboard.writeText(window.location.href)
      alert('تم نسخ الرابط إلى الحافظة')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-iraqi-blue via-blue-800 to-iraqi-blue text-white py-20 overflow-hidden">
        {/* خلفية زخرفية */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-10 w-32 h-32 border border-white rounded-full"></div>
          <div className="absolute bottom-10 left-10 w-24 h-24 border border-white rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border border-white rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              الأخبار والأنشطة
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto">
              تابع أحدث الأنشطة والفعاليات والقرارات المهمة للنائب العراقي د. أحمد محمد العراقي
            </p>

            {/* إحصائيات سريعة */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-iraqi-gold">{allNews.length}</div>
                <div className="text-blue-100">إجمالي الأخبار</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-iraqi-gold">{categories.length - 1}</div>
                <div className="text-blue-100">الفئات</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-iraqi-gold">
                  {allNews.reduce((sum, news) => sum + news.views, 0).toLocaleString()}
                </div>
                <div className="text-blue-100">المشاهدات</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-iraqi-gold">
                  {allNews.reduce((sum, news) => sum + news.likes, 0).toLocaleString()}
                </div>
                <div className="text-blue-100">الإعجابات</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* الأخبار العاجلة */}
        {urgentNews.length > 0 && (
          <section className="mb-12" data-animate id="urgent-news">
            <div className="bg-red-50 border-r-4 border-iraqi-red p-6 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-8 h-8 bg-iraqi-red rounded-full flex items-center justify-center ml-3">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
                <h2 className="text-xl font-bold text-iraqi-red">أخبار عاجلة</h2>
              </div>
              <div className="space-y-3">
                {urgentNews.slice(0, 3).map((news) => (
                  <div key={news.id} className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 cursor-pointer" onClick={() => handleNewsClick(news)}>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 hover:text-iraqi-blue transition-colors duration-300">
                        {news.title}
                      </h3>
                      <div className="flex items-center text-sm text-gray-500 mt-1">
                        <Clock className="w-4 h-4 ml-1" />
                        {news.date} - {news.time}
                      </div>
                    </div>
                    <ChevronLeft className="w-5 h-5 text-gray-400" />
                  </div>
                ))}
              </div>
            </div>
          </section>
        )}

        {/* أدوات البحث والتصفية المتطورة */}
        <section className="mb-12" data-animate id="search-filters">
          <div className="bg-white rounded-xl shadow-lg p-6">
            {/* شريط البحث */}
            <div className="relative mb-6">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="ابحث في الأخبار والأنشطة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-12 pl-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent text-right"
              />
            </div>

            {/* فلاتر الفئات */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Filter className="w-5 h-5 ml-2" />
                تصفية حسب الفئة
              </h3>
              <div className="flex flex-wrap gap-3">
                {categories.map((category) => {
                  const IconComponent = category.icon
                  const isSelected = selectedCategory === category.id
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(isSelected ? 'all' : category.id)}
                      className={`flex items-center px-4 py-2 rounded-full transition-all duration-300 ${
                        isSelected
                          ? `${category.color} text-white shadow-lg scale-105`
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <IconComponent className="w-4 h-4 ml-2" />
                      {category.name}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* أدوات الترتيب والعرض */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              {/* خيارات الترتيب */}
              <div className="flex items-center gap-4">
                <span className="text-gray-700 font-medium">ترتيب حسب:</span>
                <div className="flex gap-2">
                  {sortOptions.map((option) => {
                    const IconComponent = option.icon
                    const isSelected = sortBy === option.value
                    return (
                      <button
                        key={option.value}
                        onClick={() => setSortBy(option.value)}
                        className={`flex items-center px-3 py-2 rounded-lg transition-all duration-300 ${
                          isSelected
                            ? 'bg-iraqi-blue text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        <IconComponent className="w-4 h-4 ml-1" />
                        {option.label}
                      </button>
                    )
                  })}
                </div>
              </div>

              {/* خيارات العرض */}
              <div className="flex items-center gap-4">
                <span className="text-gray-700 font-medium">طريقة العرض:</span>
                <div className="flex gap-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-all duration-300 ${
                      viewMode === 'grid'
                        ? 'bg-iraqi-blue text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Grid className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-all duration-300 ${
                      viewMode === 'list'
                        ? 'bg-iraqi-blue text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <List className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* نتائج البحث */}
            <div className="mt-4 text-sm text-gray-600">
              عرض {paginatedNews.length} من أصل {sortedNews.length} خبر
              {searchTerm && ` للبحث عن "${searchTerm}"`}
              {selectedCategory && selectedCategory !== 'all' && ` في فئة "${categories.find(c => c.id === selectedCategory)?.name}"`}
            </div>
          </div>
        </section>

        {/* الأخبار المميزة */}
        {featuredNews.length > 0 && (
          <section className="mb-12" data-animate id="featured-news">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-3xl font-bold text-gray-900 flex items-center">
                <Star className="w-8 h-8 text-iraqi-gold ml-3" />
                الأخبار المميزة
              </h2>
              <div className="text-sm text-gray-600">
                {featuredNews.length} خبر مميز
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredNews.map((news, index) => (
                <div
                  key={news.id}
                  className="group relative bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                  onClick={() => handleNewsClick(news)}
                >
                  {/* صورة الخبر */}
                  <div className="relative h-64 overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-iraqi-blue to-blue-700 flex items-center justify-center">
                      <span className="text-white text-6xl font-bold opacity-20">
                        {index + 1}
                      </span>
                    </div>

                    {/* شارات */}
                    <div className="absolute top-4 right-4 flex gap-2">
                      {news.featured && (
                        <span className="bg-iraqi-gold text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                          <Star className="w-4 h-4 ml-1" />
                          مميز
                        </span>
                      )}
                      {news.urgent && (
                        <span className="bg-iraqi-red text-white px-3 py-1 rounded-full text-sm font-medium">
                          عاجل
                        </span>
                      )}
                    </div>

                    {/* تدرج في الأسفل */}
                    <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-black/60 to-transparent"></div>
                  </div>

                  {/* محتوى الخبر */}
                  <div className="p-6">
                    {/* فئة الخبر */}
                    <div className="flex items-center justify-between mb-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium text-white ${
                        categories.find(c => c.id === news.category)?.color || 'bg-gray-500'
                      }`}>
                        {categories.find(c => c.id === news.category)?.name || news.category}
                      </span>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="w-4 h-4 ml-1" />
                        {news.readTime}
                      </div>
                    </div>

                    {/* عنوان الخبر */}
                    <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-iraqi-blue transition-colors duration-300 line-clamp-2">
                      {news.title}
                    </h3>

                    {/* مقتطف الخبر */}
                    <p className="text-gray-600 mb-4 line-clamp-3 leading-relaxed">
                      {news.excerpt}
                    </p>

                    {/* معلومات إضافية */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 ml-1" />
                        {news.date}
                      </div>
                      <div className="flex items-center">
                        <MapPin className="w-4 h-4 ml-1" />
                        {news.location}
                      </div>
                    </div>

                    {/* إحصائيات التفاعل */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center text-gray-500">
                          <Eye className="w-4 h-4 ml-1" />
                          {news.views.toLocaleString()}
                        </div>
                        <div className="flex items-center text-gray-500">
                          <Heart className="w-4 h-4 ml-1" />
                          {news.likes.toLocaleString()}
                        </div>
                        <div className="flex items-center text-gray-500">
                          <MessageSquare className="w-4 h-4 ml-1" />
                          {news.comments}
                        </div>
                      </div>

                      {/* أزرار التفاعل */}
                      <div className="flex items-center gap-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleLike(news.id)
                          }}
                          className={`p-2 rounded-full transition-all duration-300 ${
                            likedNews.includes(news.id)
                              ? 'bg-red-100 text-red-500'
                              : 'bg-gray-100 text-gray-500 hover:bg-red-100 hover:text-red-500'
                          }`}
                        >
                          <Heart className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleBookmark(news.id)
                          }}
                          className={`p-2 rounded-full transition-all duration-300 ${
                            bookmarkedNews.includes(news.id)
                              ? 'bg-iraqi-blue text-white'
                              : 'bg-gray-100 text-gray-500 hover:bg-iraqi-blue hover:text-white'
                          }`}
                        >
                          <Bookmark className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleShare(news)
                          }}
                          className="p-2 rounded-full bg-gray-100 text-gray-500 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                        >
                          <Share2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}



        {/* قائمة الأخبار الرئيسية */}
        <section data-animate id="main-news">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900">جميع الأخبار</h2>
            <div className="text-sm text-gray-600">
              صفحة {currentPage} من {totalPages}
            </div>
          </div>

          {/* عرض شبكي */}
          {viewMode === 'grid' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {paginatedNews.map((news, index) => (
                <div
                  key={news.id}
                  className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                  onClick={() => handleNewsClick(news)}
                >
                  {/* صورة الخبر */}
                  <div className="relative h-48 overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                      <span className="text-gray-400 text-4xl font-bold">
                        {startIndex + index + 1}
                      </span>
                    </div>

                    {/* شارات */}
                    <div className="absolute top-3 right-3 flex gap-2">
                      {news.urgent && (
                        <span className="bg-iraqi-red text-white px-2 py-1 rounded-full text-xs font-medium">
                          عاجل
                        </span>
                      )}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${
                        categories.find(c => c.id === news.category)?.color || 'bg-gray-500'
                      }`}>
                        {categories.find(c => c.id === news.category)?.name || news.category}
                      </span>
                    </div>
                  </div>

                  {/* محتوى الخبر */}
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-iraqi-blue transition-colors duration-300 line-clamp-2">
                      {news.title}
                    </h3>

                    <p className="text-gray-600 mb-4 line-clamp-3 text-sm leading-relaxed">
                      {news.excerpt}
                    </p>

                    {/* معلومات الخبر */}
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                      <div className="flex items-center">
                        <Calendar className="w-3 h-3 ml-1" />
                        {news.date}
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-3 h-3 ml-1" />
                        {news.readTime}
                      </div>
                    </div>

                    {/* إحصائيات وأزرار */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center gap-3 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Eye className="w-3 h-3 ml-1" />
                          {news.views.toLocaleString()}
                        </div>
                        <div className="flex items-center">
                          <Heart className="w-3 h-3 ml-1" />
                          {news.likes}
                        </div>
                      </div>

                      <div className="flex items-center gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleLike(news.id)
                          }}
                          className={`p-1.5 rounded-full transition-all duration-300 ${
                            likedNews.includes(news.id)
                              ? 'bg-red-100 text-red-500'
                              : 'bg-gray-100 text-gray-500 hover:bg-red-100 hover:text-red-500'
                          }`}
                        >
                          <Heart className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleBookmark(news.id)
                          }}
                          className={`p-1.5 rounded-full transition-all duration-300 ${
                            bookmarkedNews.includes(news.id)
                              ? 'bg-iraqi-blue text-white'
                              : 'bg-gray-100 text-gray-500 hover:bg-iraqi-blue hover:text-white'
                          }`}
                        >
                          <Bookmark className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* عرض قائمة */}
          {viewMode === 'list' && (
            <div className="space-y-6">
              {paginatedNews.map((news, index) => (
                <div
                  key={news.id}
                  className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                  onClick={() => handleNewsClick(news)}
                >
                  <div className="flex">
                    {/* صورة الخبر */}
                    <div className="w-48 h-32 flex-shrink-0 relative overflow-hidden">
                      <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                        <span className="text-gray-400 text-2xl font-bold">
                          {startIndex + index + 1}
                        </span>
                      </div>

                      {/* شارات */}
                      <div className="absolute top-2 right-2 flex gap-1">
                        {news.urgent && (
                          <span className="bg-iraqi-red text-white px-2 py-1 rounded-full text-xs font-medium">
                            عاجل
                          </span>
                        )}
                      </div>
                    </div>

                    {/* محتوى الخبر */}
                    <div className="flex-1 p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          {/* فئة الخبر */}
                          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium text-white mb-3 ${
                            categories.find(c => c.id === news.category)?.color || 'bg-gray-500'
                          }`}>
                            {categories.find(c => c.id === news.category)?.name || news.category}
                          </span>

                          <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-iraqi-blue transition-colors duration-300 line-clamp-2">
                            {news.title}
                          </h3>

                          <p className="text-gray-600 mb-4 line-clamp-2 leading-relaxed">
                            {news.excerpt}
                          </p>

                          {/* معلومات الخبر */}
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 ml-1" />
                              {news.date} - {news.time}
                            </div>
                            <div className="flex items-center">
                              <User className="w-4 h-4 ml-1" />
                              {news.author}
                            </div>
                            <div className="flex items-center">
                              <Clock className="w-4 h-4 ml-1" />
                              {news.readTime}
                            </div>
                          </div>
                        </div>

                        {/* إحصائيات وأزرار */}
                        <div className="flex flex-col items-end gap-3">
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Eye className="w-4 h-4 ml-1" />
                              {news.views.toLocaleString()}
                            </div>
                            <div className="flex items-center">
                              <Heart className="w-4 h-4 ml-1" />
                              {news.likes}
                            </div>
                            <div className="flex items-center">
                              <MessageSquare className="w-4 h-4 ml-1" />
                              {news.comments}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleLike(news.id)
                              }}
                              className={`p-2 rounded-full transition-all duration-300 ${
                                likedNews.includes(news.id)
                                  ? 'bg-red-100 text-red-500'
                                  : 'bg-gray-100 text-gray-500 hover:bg-red-100 hover:text-red-500'
                              }`}
                            >
                              <Heart className="w-4 h-4" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleBookmark(news.id)
                              }}
                              className={`p-2 rounded-full transition-all duration-300 ${
                                bookmarkedNews.includes(news.id)
                                  ? 'bg-iraqi-blue text-white'
                                  : 'bg-gray-100 text-gray-500 hover:bg-iraqi-blue hover:text-white'
                              }`}
                            >
                              <Bookmark className="w-4 h-4" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleShare(news)
                              }}
                              className="p-2 rounded-full bg-gray-100 text-gray-500 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                            >
                              <Share2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </section>

        {/* رسالة عدم وجود نتائج */}
        {sortedNews.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد نتائج</h3>
            <p className="text-gray-600">
              لم يتم العثور على أخبار تطابق معايير البحث المحددة
            </p>
          </div>
        )}

        {/* إحصائيات الأخبار */}
        <div className="mt-12 bg-iraqi-blue text-white rounded-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <h3 className="text-3xl font-bold mb-2">{newsData.length}</h3>
              <p className="text-blue-100">إجمالي الأخبار</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold mb-2">{categories.length - 1}</h3>
              <p className="text-blue-100">الفئات المتاحة</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold mb-2">
                {newsData.reduce((sum, news) => sum + news.views, 0).toLocaleString()}
              </h3>
              <p className="text-blue-100">إجمالي المشاهدات</p>
            </div>
          </div>
        </div>
        {/* نظام الصفحات */}
        {totalPages > 1 && (
          <section className="mt-12" data-animate id="pagination">
            <div className="flex items-center justify-center">
              <div className="flex items-center gap-2">
                {/* الصفحة السابقة */}
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="flex items-center px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                >
                  <ChevronRight className="w-4 h-4 ml-1" />
                  السابق
                </button>

                {/* أرقام الصفحات */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                    if (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 2 && page <= currentPage + 2)
                    ) {
                      return (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`w-10 h-10 rounded-lg font-medium transition-all duration-300 ${
                            currentPage === page
                              ? 'bg-iraqi-blue text-white shadow-lg'
                              : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {page}
                        </button>
                      )
                    } else if (
                      page === currentPage - 3 ||
                      page === currentPage + 3
                    ) {
                      return (
                        <span key={page} className="px-2 text-gray-400">
                          ...
                        </span>
                      )
                    }
                    return null
                  })}
                </div>

                {/* الصفحة التالية */}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="flex items-center px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                >
                  التالي
                  <ChevronLeft className="w-4 h-4 mr-1" />
                </button>
              </div>
            </div>
          </section>
        )}

        {/* مودال تفاصيل الخبر */}
        {showNewsModal && selectedNews && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              {/* رأس المودال */}
              <div className="sticky top-0 bg-white border-b border-gray-200 p-6 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium text-white ${
                    categories.find(c => c.id === selectedNews.category)?.color || 'bg-gray-500'
                  }`}>
                    {categories.find(c => c.id === selectedNews.category)?.name || selectedNews.category}
                  </span>
                  {selectedNews.urgent && (
                    <span className="bg-iraqi-red text-white px-3 py-1 rounded-full text-sm font-medium">
                      عاجل
                    </span>
                  )}
                </div>
                <button
                  onClick={() => setShowNewsModal(false)}
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-300"
                >
                  <span className="text-2xl">&times;</span>
                </button>
              </div>

              {/* محتوى المودال */}
              <div className="p-6">
                {/* عنوان الخبر */}
                <h1 className="text-3xl font-bold text-gray-900 mb-6 leading-tight">
                  {selectedNews.title}
                </h1>

                {/* معلومات الخبر */}
                <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-6 pb-6 border-b border-gray-200">
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 ml-1" />
                    {selectedNews.date} - {selectedNews.time}
                  </div>
                  <div className="flex items-center">
                    <User className="w-4 h-4 ml-1" />
                    {selectedNews.author}
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 ml-1" />
                    {selectedNews.location}
                  </div>
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 ml-1" />
                    {selectedNews.readTime}
                  </div>
                </div>

                {/* صورة الخبر */}
                <div className="w-full h-64 bg-gradient-to-br from-gray-200 to-gray-300 rounded-lg mb-6 flex items-center justify-center">
                  <span className="text-gray-400 text-6xl font-bold">
                    {selectedNews.id}
                  </span>
                </div>

                {/* محتوى الخبر */}
                <div className="prose prose-lg max-w-none mb-8">
                  <div className="text-gray-700 leading-relaxed whitespace-pre-line">
                    {selectedNews.content}
                  </div>
                </div>

                {/* العلامات */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">العلامات</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedNews.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-iraqi-blue hover:text-white transition-colors duration-300 cursor-pointer"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* إحصائيات التفاعل */}
                <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                  <div className="flex items-center gap-6 text-gray-600">
                    <div className="flex items-center">
                      <Eye className="w-5 h-5 ml-2" />
                      <span className="font-medium">{selectedNews.views.toLocaleString()}</span>
                      <span className="mr-1">مشاهدة</span>
                    </div>
                    <div className="flex items-center">
                      <Heart className="w-5 h-5 ml-2" />
                      <span className="font-medium">{selectedNews.likes}</span>
                      <span className="mr-1">إعجاب</span>
                    </div>
                    <div className="flex items-center">
                      <MessageSquare className="w-5 h-5 ml-2" />
                      <span className="font-medium">{selectedNews.comments}</span>
                      <span className="mr-1">تعليق</span>
                    </div>
                    <div className="flex items-center">
                      <Share2 className="w-5 h-5 ml-2" />
                      <span className="font-medium">{selectedNews.shares}</span>
                      <span className="mr-1">مشاركة</span>
                    </div>
                  </div>

                  {/* أزرار التفاعل */}
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => handleLike(selectedNews.id)}
                      className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                        likedNews.includes(selectedNews.id)
                          ? 'bg-red-100 text-red-600'
                          : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                      }`}
                    >
                      <Heart className="w-4 h-4 ml-1" />
                      إعجاب
                    </button>
                    <button
                      onClick={() => handleBookmark(selectedNews.id)}
                      className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                        bookmarkedNews.includes(selectedNews.id)
                          ? 'bg-iraqi-blue text-white'
                          : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                      }`}
                    >
                      <Bookmark className="w-4 h-4 ml-1" />
                      حفظ
                    </button>
                    <button
                      onClick={() => handleShare(selectedNews)}
                      className="flex items-center px-4 py-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                    >
                      <Share2 className="w-4 h-4 ml-1" />
                      مشاركة
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

      </div>
    </div>
  )
}

export default News
