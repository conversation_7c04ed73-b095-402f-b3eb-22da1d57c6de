import React, { useState } from 'react'
import { PageHeader, SearchFilter, NewsCard } from '../components'

const News = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [sortBy, setSortBy] = useState('')

  const categories = [
    'قوانين',
    'زيارات',
    'اجتماعات',
    'بيانات',
    'فعاليات',
    'مؤتمرات'
  ]

  const sortOptions = [
    { value: 'newest', label: 'الأحدث' },
    { value: 'oldest', label: 'الأقدم' },
    { value: 'most_viewed', label: 'الأكثر مشاهدة' }
  ]

  const newsData = [
    {
      id: 1,
      title: 'مناقشة قانون الضمان الاجتماعي الجديد في البرلمان',
      excerpt: 'تم مناقشة مشروع قانون الضمان الاجتماعي الجديد في جلسة البرلمان اليوم، والذي يهدف إلى تحسين أوضاع المواطنين ذوي الدخل المحدود...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-15',
      time: '14:30',
      category: 'قوانين',
      views: 1250,
      image: '/images/news1.jpg',
      featured: true
    },
    {
      id: 2,
      title: 'زيارة ميدانية لمشروع الإسكان الحكومي في منطقة الكرادة',
      excerpt: 'قام النائب بزيارة ميدانية لمتابعة تقدم مشروع الإسكان الحكومي في منطقة الكرادة، والاطلاع على سير العمل والتحديات...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-12',
      time: '10:15',
      category: 'زيارات',
      views: 890,
      image: '/images/news2.jpg',
      featured: false
    },
    {
      id: 3,
      title: 'اجتماع لجنة الخدمات والإعمار لمناقشة المشاريع الجديدة',
      excerpt: 'عُقد اجتماع لجنة الخدمات والإعمار لمناقشة المشاريع الجديدة المقترحة لتطوير البنية التحتية في المحافظة...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-10',
      time: '09:00',
      category: 'اجتماعات',
      views: 675,
      image: '/images/news3.jpg',
      featured: false
    },
    {
      id: 4,
      title: 'بيان حول أزمة المياه في المحافظات الجنوبية',
      excerpt: 'أصدر النائب بياناً حول أزمة المياه في المحافظات الجنوبية، مطالباً بحلول عاجلة لمعالجة هذه المشكلة الحيوية...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-08',
      time: '16:45',
      category: 'بيانات',
      views: 1100,
      image: '/images/news4.jpg',
      featured: false
    },
    {
      id: 5,
      title: 'مشاركة في مؤتمر التنمية المستدامة في دبي',
      excerpt: 'شارك النائب في مؤتمر التنمية المستدامة المنعقد في دبي، حيث قدم ورقة عمل حول التحديات البيئية في العراق...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-05',
      time: '11:20',
      category: 'مؤتمرات',
      views: 520,
      image: '/images/news5.jpg',
      featured: false
    },
    {
      id: 6,
      title: 'فعالية توزيع المساعدات على الأسر المحتاجة',
      excerpt: 'نظم مكتب النائب فعالية لتوزيع المساعدات الغذائية والمالية على الأسر المحتاجة في المنطقة، بمشاركة منظمات المجتمع المدني...',
      content: 'محتوى مفصل للخبر...',
      date: '2025-01-03',
      time: '13:00',
      category: 'فعاليات',
      views: 780,
      image: '/images/news6.jpg',
      featured: false
    }
  ]

  // تصفية الأخبار حسب البحث والفئة
  const filteredNews = newsData.filter(news => {
    const matchesSearch = news.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         news.excerpt.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = !selectedCategory || news.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // ترتيب الأخبار
  const sortedNews = [...filteredNews].sort((a, b) => {
    switch (sortBy) {
      case 'oldest':
        return new Date(a.date) - new Date(b.date)
      case 'most_viewed':
        return b.views - a.views
      default: // newest
        return new Date(b.date) - new Date(a.date)
    }
  })

  const featuredNews = newsData.filter(news => news.featured)

  const handleNewsClick = (news) => {
    console.log('News clicked:', news)
    // هنا يمكن التنقل إلى صفحة تفاصيل الخبر
  }

  return (
    <div className="min-h-screen">
      {/* رأس الصفحة */}
      <PageHeader
        title="الأخبار والأنشطة"
        subtitle="تابع أحدث الأنشطة والفعاليات والقرارات المهمة للنائب العراقي"
        breadcrumbs={[
          { label: 'الأخبار' }
        ]}
      />

      <div className="container-custom py-12">
        {/* أدوات البحث والتصفية */}
        <SearchFilter
          searchValue={searchTerm}
          onSearchChange={setSearchTerm}
          searchPlaceholder="ابحث في الأخبار..."
          categories={categories}
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
          sortOptions={sortOptions}
          selectedSort={sortBy}
          onSortChange={setSortBy}
        />

        {/* الأخبار المميزة */}
        {featuredNews.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">الأخبار المميزة</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredNews.map((news) => (
                <NewsCard
                  key={news.id}
                  title={news.title}
                  excerpt={news.excerpt}
                  category={news.category}
                  date={news.date}
                  time={news.time}
                  views={news.views}
                  image={news.image}
                  featured={news.featured}
                  onClick={() => handleNewsClick(news)}
                />
              ))}
            </div>
          </section>
        )}



        {/* قائمة الأخبار */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sortedNews.map((news) => (
            <NewsCard
              key={news.id}
              title={news.title}
              excerpt={news.excerpt}
              category={news.category}
              date={news.date}
              time={news.time}
              views={news.views}
              image={news.image}
              onClick={() => handleNewsClick(news)}
            />
          ))}
        </div>

        {/* رسالة عدم وجود نتائج */}
        {sortedNews.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد نتائج</h3>
            <p className="text-gray-600">
              لم يتم العثور على أخبار تطابق معايير البحث المحددة
            </p>
          </div>
        )}

        {/* إحصائيات الأخبار */}
        <div className="mt-12 bg-iraqi-blue text-white rounded-lg p-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <h3 className="text-3xl font-bold mb-2">{newsData.length}</h3>
              <p className="text-blue-100">إجمالي الأخبار</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold mb-2">{categories.length - 1}</h3>
              <p className="text-blue-100">الفئات المتاحة</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold mb-2">
                {newsData.reduce((sum, news) => sum + news.views, 0).toLocaleString()}
              </h3>
              <p className="text-blue-100">إجمالي المشاهدات</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default News
