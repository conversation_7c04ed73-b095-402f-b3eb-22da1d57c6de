import React, { useState, useEffect } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { Eye, EyeOff, Lock, User, Shield } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { Button, Input, Card, Alert } from '../../components/UI'

const AdminLogin = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const { login, user } = useAuth()
  const location = useLocation()

  // إذا كان المستخدم مسجل دخول بالفعل، توجيهه للداشبورد
  if (user) {
    const from = location.state?.from?.pathname || '/admin'
    return <Navigate to={from} replace />
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // إزالة رسالة الخطأ عند بدء الكتابة
    if (error) setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const result = await login(formData.username, formData.password)
      
      if (result.success) {
        // سيتم التوجيه تلقائياً بواسطة useEffect أعلاه
      } else {
        setError(result.error || 'حدث خطأ أثناء تسجيل الدخول')
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-iraqi-blue to-blue-800 flex items-center justify-center p-4">
      {/* خلفية زخرفية */}
      <div className="absolute inset-0 bg-black opacity-20"></div>
      
      <div className="relative z-10 w-full max-w-md">
        {/* شعار وعنوان */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-iraqi-gold rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <Shield className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">
            لوحة التحكم
          </h1>
          <p className="text-blue-100">
            موقع النائب العراقي الرسمي
          </p>
        </div>

        {/* نموذج تسجيل الدخول */}
        <Card className="shadow-2xl">
          <Card.Header>
            <Card.Title className="text-center text-gray-900">
              تسجيل الدخول
            </Card.Title>
            <Card.Description className="text-center">
              أدخل بيانات الدخول للوصول إلى لوحة التحكم
            </Card.Description>
          </Card.Header>

          <Card.Content>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* رسالة الخطأ */}
              {error && (
                <Alert variant="error" title="خطأ في تسجيل الدخول">
                  {error}
                </Alert>
              )}

              {/* اسم المستخدم */}
              <Input
                label="اسم المستخدم"
                name="username"
                type="text"
                value={formData.username}
                onChange={handleInputChange}
                placeholder="أدخل اسم المستخدم"
                icon={User}
                required
                disabled={isLoading}
              />

              {/* كلمة المرور */}
              <div className="relative">
                <Input
                  label="كلمة المرور"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="أدخل كلمة المرور"
                  icon={Lock}
                  required
                  disabled={isLoading}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>

              {/* زر تسجيل الدخول */}
              <Button
                type="submit"
                variant="primary"
                size="lg"
                className="w-full"
                loading={isLoading}
                disabled={!formData.username || !formData.password}
              >
                {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
              </Button>
            </form>
          </Card.Content>

          <Card.Footer>
            {/* معلومات تجريبية */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                بيانات تجريبية للاختبار:
              </h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
              </div>
            </div>
          </Card.Footer>
        </Card>

        {/* معلومات إضافية */}
        <div className="text-center mt-6 text-blue-100 text-sm">
          <p>© 2025 موقع النائب العراقي الرسمي</p>
          <p>جميع الحقوق محفوظة</p>
        </div>
      </div>
    </div>
  )
}

export default AdminLogin
