import React from 'react'
import { TrendingUp, TrendingDown } from 'lucide-react'

const StatsCard = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  trendValue,
  color = 'iraqi-blue',
  className = '',
  ...props
}) => {
  const colorClasses = {
    'iraqi-blue': 'bg-iraqi-blue text-white',
    'iraqi-gold': 'bg-iraqi-gold text-white',
    'iraqi-green': 'bg-iraqi-green text-white',
    'iraqi-red': 'bg-iraqi-red text-white',
    'gray': 'bg-gray-600 text-white'
  }
  
  const trendColors = {
    up: 'text-green-500',
    down: 'text-red-500',
    neutral: 'text-gray-500'
  }
  
  const formatNumber = (num) => {
    if (typeof num !== 'number') return num
    
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'م'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'ك'
    }
    return num.toLocaleString('ar-IQ')
  }
  
  return (
    <div 
      className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden ${className}`}
      {...props}
    >
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600 mb-1">
              {title}
            </p>
            <p className="text-3xl font-bold text-gray-900 mb-1">
              {formatNumber(value)}
            </p>
            {subtitle && (
              <p className="text-sm text-gray-500">
                {subtitle}
              </p>
            )}
            
            {/* مؤشر الاتجاه */}
            {trend && trendValue && (
              <div className={`flex items-center mt-2 text-sm ${trendColors[trend]}`}>
                {trend === 'up' && <TrendingUp className="w-4 h-4 ml-1" />}
                {trend === 'down' && <TrendingDown className="w-4 h-4 ml-1" />}
                <span>{trendValue}</span>
              </div>
            )}
          </div>
          
          {Icon && (
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>
              <Icon className="w-6 h-6" />
            </div>
          )}
        </div>
      </div>
      
      {/* شريط ملون في الأسفل */}
      <div className={`h-1 ${colorClasses[color]}`}></div>
    </div>
  )
}

export default StatsCard
