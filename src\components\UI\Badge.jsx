import React from 'react'

const Badge = ({
  children,
  variant = 'default',
  size = 'md',
  className = '',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center font-medium rounded-full'
  
  const variants = {
    default: 'bg-gray-100 text-gray-800',
    primary: 'bg-iraqi-blue text-white',
    secondary: 'bg-iraqi-gold text-white',
    success: 'bg-iraqi-green text-white',
    danger: 'bg-iraqi-red text-white',
    warning: 'bg-yellow-100 text-yellow-800',
    info: 'bg-blue-100 text-blue-800',
    outline: 'border border-gray-300 text-gray-700 bg-transparent',
    'outline-primary': 'border border-iraqi-blue text-iraqi-blue bg-transparent',
    'outline-success': 'border border-iraqi-green text-iraqi-green bg-transparent',
    'outline-danger': 'border border-iraqi-red text-iraqi-red bg-transparent'
  }
  
  const sizes = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  }
  
  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`
  
  return (
    <span className={classes} {...props}>
      {children}
    </span>
  )
}

export default Badge
