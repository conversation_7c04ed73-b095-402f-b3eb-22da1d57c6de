import React, { useState } from 'react'
import { 
  Search, 
  Filter, 
  Calendar, 
  Play,
  Image as ImageIcon,
  Video,
  Eye,
  Download,
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

const Gallery = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('الكل')
  const [selectedType, setSelectedType] = useState('الكل')
  const [selectedImage, setSelectedImage] = useState(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  const categories = ['الكل', 'فعاليات', 'زيارات', 'اجتماعات', 'مؤتمرات', 'احتفالات']
  const types = ['الكل', 'صور', 'فيديوهات']

  const galleryData = [
    {
      id: 1,
      title: 'جلسة مناقشة قانون الضمان الاجتماعي',
      description: 'صور من جلسة مناقشة قانون الضمان الاجتماعي في البرلمان',
      category: 'اجتماعات',
      type: 'صور',
      date: '2025-01-15',
      thumbnail: '/images/gallery/thumb1.jpg',
      fullImage: '/images/gallery/full1.jpg',
      views: 1250,
      downloads: 45
    },
    {
      id: 2,
      title: 'زيارة مشروع الإسكان الحكومي',
      description: 'فيديو من الزيارة الميدانية لمشروع الإسكان الحكومي',
      category: 'زيارات',
      type: 'فيديوهات',
      date: '2025-01-12',
      thumbnail: '/images/gallery/thumb2.jpg',
      videoUrl: '/videos/housing-visit.mp4',
      duration: '05:30',
      views: 890,
      downloads: 12
    },
    {
      id: 3,
      title: 'فعالية توزيع المساعدات',
      description: 'صور من فعالية توزيع المساعدات على الأسر المحتاجة',
      category: 'فعاليات',
      type: 'صور',
      date: '2025-01-10',
      thumbnail: '/images/gallery/thumb3.jpg',
      fullImage: '/images/gallery/full3.jpg',
      views: 675,
      downloads: 28
    },
    {
      id: 4,
      title: 'مؤتمر التنمية المستدامة',
      description: 'مشاركة في مؤتمر التنمية المستدامة في دبي',
      category: 'مؤتمرات',
      type: 'صور',
      date: '2025-01-05',
      thumbnail: '/images/gallery/thumb4.jpg',
      fullImage: '/images/gallery/full4.jpg',
      views: 520,
      downloads: 15
    },
    {
      id: 5,
      title: 'احتفالية يوم الشهيد',
      description: 'فيديو من احتفالية يوم الشهيد العراقي',
      category: 'احتفالات',
      type: 'فيديوهات',
      date: '2024-12-01',
      thumbnail: '/images/gallery/thumb5.jpg',
      videoUrl: '/videos/martyr-day.mp4',
      duration: '08:15',
      views: 1100,
      downloads: 35
    },
    {
      id: 6,
      title: 'اجتماع لجنة الخدمات',
      description: 'صور من اجتماع لجنة الخدمات والإعمار',
      category: 'اجتماعات',
      type: 'صور',
      date: '2024-11-28',
      thumbnail: '/images/gallery/thumb6.jpg',
      fullImage: '/images/gallery/full6.jpg',
      views: 780,
      downloads: 22
    }
  ]

  const filteredGallery = galleryData.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'الكل' || item.category === selectedCategory
    const matchesType = selectedType === 'الكل' || item.type === selectedType
    return matchesSearch && matchesCategory && matchesType
  })

  const images = filteredGallery.filter(item => item.type === 'صور')

  const openImageModal = (item, index) => {
    setSelectedImage(item)
    setCurrentImageIndex(index)
  }

  const closeImageModal = () => {
    setSelectedImage(null)
    setCurrentImageIndex(0)
  }

  const navigateImage = (direction) => {
    const newIndex = direction === 'next' 
      ? (currentImageIndex + 1) % images.length
      : (currentImageIndex - 1 + images.length) % images.length
    
    setCurrentImageIndex(newIndex)
    setSelectedImage(images[newIndex])
  }

  const totalViews = galleryData.reduce((sum, item) => sum + item.views, 0)
  const totalDownloads = galleryData.reduce((sum, item) => sum + item.downloads, 0)
  const photosCount = galleryData.filter(item => item.type === 'صور').length
  const videosCount = galleryData.filter(item => item.type === 'فيديوهات').length

  return (
    <div className="min-h-screen py-12">
      <div className="container-custom">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">المعرض</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            مجموعة شاملة من الصور والفيديوهات التي توثق أنشطة وفعاليات النائب العراقي
          </p>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="card text-center">
            <div className="w-12 h-12 bg-iraqi-blue rounded-full mx-auto mb-4 flex items-center justify-center">
              <ImageIcon className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{photosCount}</h3>
            <p className="text-gray-600">الصور</p>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-iraqi-gold rounded-full mx-auto mb-4 flex items-center justify-center">
              <Video className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{videosCount}</h3>
            <p className="text-gray-600">الفيديوهات</p>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-iraqi-green rounded-full mx-auto mb-4 flex items-center justify-center">
              <Eye className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{totalViews.toLocaleString()}</h3>
            <p className="text-gray-600">المشاهدات</p>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-iraqi-red rounded-full mx-auto mb-4 flex items-center justify-center">
              <Download className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{totalDownloads}</h3>
            <p className="text-gray-600">التحميلات</p>
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="ابحث في المعرض..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
              />
            </div>

            <div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    الفئة: {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
              >
                {types.map((type) => (
                  <option key={type} value={type}>
                    النوع: {type}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* شبكة المعرض */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredGallery.map((item, index) => (
            <div key={item.id} className="card hover:shadow-xl transition-shadow duration-300 overflow-hidden">
              <div className="relative group">
                {/* الصورة المصغرة */}
                <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden">
                  <div className="w-full h-48 bg-gray-300 flex items-center justify-center">
                    {item.type === 'صور' ? (
                      <ImageIcon className="w-12 h-12 text-gray-500" />
                    ) : (
                      <Video className="w-12 h-12 text-gray-500" />
                    )}
                  </div>
                </div>

                {/* تراكب التفاعل */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex space-x-4 space-x-reverse">
                    {item.type === 'صور' ? (
                      <button
                        onClick={() => openImageModal(item, images.findIndex(img => img.id === item.id))}
                        className="bg-white text-gray-900 p-3 rounded-full hover:bg-gray-100 transition-colors"
                      >
                        <Eye className="w-5 h-5" />
                      </button>
                    ) : (
                      <button className="bg-white text-gray-900 p-3 rounded-full hover:bg-gray-100 transition-colors">
                        <Play className="w-5 h-5" />
                      </button>
                    )}
                    <button className="bg-white text-gray-900 p-3 rounded-full hover:bg-gray-100 transition-colors">
                      <Download className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                {/* نوع المحتوى */}
                <div className="absolute top-3 right-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    item.type === 'صور' 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {item.type === 'فيديوهات' && item.duration && (
                      <span className="mr-1">{item.duration}</span>
                    )}
                    {item.type === 'صور' ? <ImageIcon className="w-3 h-3 inline" /> : <Video className="w-3 h-3 inline" />}
                  </span>
                </div>
              </div>

              {/* معلومات المحتوى */}
              <div className="p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <span className="bg-iraqi-blue text-white px-3 py-1 rounded-full text-sm">
                    {item.category}
                  </span>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Calendar className="w-4 h-4 ml-1" />
                    {item.date}
                  </div>
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                  {item.title}
                </h3>
                
                <p className="text-gray-600 text-sm line-clamp-2">
                  {item.description}
                </p>
                
                <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                  <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                    <div className="flex items-center">
                      <Eye className="w-4 h-4 ml-1" />
                      {item.views}
                    </div>
                    <div className="flex items-center">
                      <Download className="w-4 h-4 ml-1" />
                      {item.downloads}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* رسالة عدم وجود نتائج */}
        {filteredGallery.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد نتائج</h3>
            <p className="text-gray-600">
              لم يتم العثور على محتوى يطابق معايير البحث المحددة
            </p>
          </div>
        )}

        {/* مودال عرض الصور */}
        {selectedImage && (
          <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
            <div className="relative max-w-4xl max-h-full">
              {/* أزرار التنقل */}
              <button
                onClick={() => navigateImage('prev')}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all"
              >
                <ChevronLeft className="w-6 h-6" />
              </button>
              
              <button
                onClick={() => navigateImage('next')}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all"
              >
                <ChevronRight className="w-6 h-6" />
              </button>

              {/* زر الإغلاق */}
              <button
                onClick={closeImageModal}
                className="absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all"
              >
                <X className="w-6 h-6" />
              </button>

              {/* الصورة */}
              <div className="bg-white rounded-lg overflow-hidden">
                <div className="aspect-w-16 aspect-h-9">
                  <div className="w-full h-96 bg-gray-300 flex items-center justify-center">
                    <ImageIcon className="w-24 h-24 text-gray-500" />
                  </div>
                </div>
                
                {/* معلومات الصورة */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{selectedImage.title}</h3>
                  <p className="text-gray-600 mb-4">{selectedImage.description}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                      <span>{selectedImage.category}</span>
                      <span>{selectedImage.date}</span>
                    </div>
                    <button className="btn-primary">
                      <Download className="w-4 h-4 ml-2" />
                      تحميل
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Gallery
