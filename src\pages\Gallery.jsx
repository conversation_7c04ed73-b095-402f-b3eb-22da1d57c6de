import React, { useState, useEffect } from 'react'
import {
  Search,
  Filter,
  Calendar,
  Play,
  Image as ImageIcon,
  Video,
  Eye,
  Download,
  X,
  ChevronLeft,
  ChevronRight,
  Heart,
  Share2,
  Bookmark,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Clock,
  User,
  MapPin,
  Tag,
  Star,
  Camera,
  Film,
  Users,
  Building,
  Award,
  Globe,
  Home,
  GraduationCap,
  Hospital,
  Handshake,
  Megaphone,
  FileText,
  Settings,
  Info,
  Plus,
  Minus,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize,
  ExternalLink,
  Copy,
  Check
} from 'lucide-react'

const Gallery = () => {
  // إدارة الحالة المتقدمة
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedType, setSelectedType] = useState('all')
  const [selectedDateRange, setSelectedDateRange] = useState('all')
  const [sortBy, setSortBy] = useState('newest')
  const [viewMode, setViewMode] = useState('grid') // grid, masonry, list
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedMedia, setSelectedMedia] = useState(null)
  const [currentMediaIndex, setCurrentMediaIndex] = useState(0)
  const [showLightbox, setShowLightbox] = useState(false)
  const [likedMedia, setLikedMedia] = useState([])
  const [bookmarkedMedia, setBookmarkedMedia] = useState([])
  const [isVisible, setIsVisible] = useState({})
  const [imageZoom, setImageZoom] = useState(1)
  const [imageRotation, setImageRotation] = useState(0)
  const [showMediaInfo, setShowMediaInfo] = useState(true)
  const itemsPerPage = 12

  // تكوين الفئات مع الأيقونات والألوان
  const categories = [
    { id: 'all', name: 'جميع الفئات', icon: Globe, color: 'bg-gray-500' },
    { id: 'events', name: 'الفعاليات والأنشطة', icon: Calendar, color: 'bg-iraqi-blue' },
    { id: 'projects', name: 'المشاريع والإنجازات', icon: Building, color: 'bg-iraqi-green' },
    { id: 'meetings', name: 'الاجتماعات واللقاءات', icon: Users, color: 'bg-purple-500' },
    { id: 'conferences', name: 'المؤتمرات والندوات', icon: Megaphone, color: 'bg-orange-500' },
    { id: 'ceremonies', name: 'الاحتفالات والمناسبات', icon: Award, color: 'bg-red-500' },
    { id: 'visits', name: 'الزيارات الميدانية', icon: MapPin, color: 'bg-indigo-500' },
    { id: 'community', name: 'الأنشطة المجتمعية', icon: Handshake, color: 'bg-pink-500' },
    { id: 'education', name: 'التعليم والثقافة', icon: GraduationCap, color: 'bg-teal-500' }
  ]

  // أنواع الوسائط
  const mediaTypes = [
    { id: 'all', name: 'جميع الأنواع', icon: Globe },
    { id: 'photos', name: 'الصور', icon: Camera },
    { id: 'videos', name: 'الفيديوهات', icon: Film }
  ]

  // نطاقات التاريخ
  const dateRanges = [
    { id: 'all', name: 'جميع التواريخ' },
    { id: 'today', name: 'اليوم' },
    { id: 'week', name: 'هذا الأسبوع' },
    { id: 'month', name: 'هذا الشهر' },
    { id: 'quarter', name: 'هذا الربع' },
    { id: 'year', name: 'هذا العام' },
    { id: 'custom', name: 'نطاق مخصص' }
  ]

  // خيارات الترتيب
  const sortOptions = [
    { value: 'newest', label: 'الأحدث أولاً' },
    { value: 'oldest', label: 'الأقدم أولاً' },
    { value: 'most_viewed', label: 'الأكثر مشاهدة' },
    { value: 'most_liked', label: 'الأكثر إعجاباً' },
    { value: 'most_downloaded', label: 'الأكثر تحميلاً' },
    { value: 'alphabetical', label: 'أبجدياً' },
    { value: 'category', label: 'حسب الفئة' }
  ]

  // بيانات المعرض الشاملة
  const galleryData = [
    {
      id: 1,
      title: 'جلسة مناقشة قانون الضمان الاجتماعي الجديد',
      description: 'صور شاملة من جلسة مناقشة قانون الضمان الاجتماعي الجديد في مجلس النواب العراقي، والتي تهدف إلى تحسين أوضاع المواطنين الاجتماعية والاقتصادية',
      category: 'meetings',
      type: 'photos',
      date: '2025-01-15',
      location: 'مجلس النواب العراقي - بغداد',
      photographer: 'أحمد محمد الكاظمي',
      tags: ['قانون', 'ضمان اجتماعي', 'مجلس النواب', 'تشريع'],
      thumbnail: '/images/gallery/thumb1.jpg',
      fullImage: '/images/gallery/full1.jpg',
      views: 2450,
      likes: 156,
      downloads: 89,
      shares: 34,
      featured: true,
      resolution: '4K',
      fileSize: '2.3 MB',
      camera: 'Canon EOS R5',
      settings: 'f/2.8, 1/125s, ISO 400'
    },
    {
      id: 2,
      title: 'زيارة مشروع الإسكان الحكومي في البصرة',
      description: 'فيديو توثيقي شامل من الزيارة الميدانية لمشروع الإسكان الحكومي في محافظة البصرة، يعرض التقدم المحرز في بناء الوحدات السكنية للمواطنين',
      category: 'visits',
      type: 'videos',
      date: '2025-01-12',
      location: 'البصرة - مشروع الإسكان الحكومي',
      photographer: 'فريق الإعلام البرلماني',
      tags: ['إسكان', 'البصرة', 'مشاريع حكومية', 'زيارة ميدانية'],
      thumbnail: '/images/gallery/thumb2.jpg',
      videoUrl: '/videos/housing-visit.mp4',
      duration: '08:45',
      views: 1890,
      likes: 234,
      downloads: 67,
      shares: 89,
      featured: true,
      resolution: '1080p',
      fileSize: '156 MB',
      format: 'MP4'
    },
    {
      id: 3,
      title: 'فعالية توزيع المساعدات الإنسانية',
      description: 'مجموعة صور من فعالية توزيع المساعدات الإنسانية والغذائية على الأسر المحتاجة في مناطق مختلفة من العراق ضمن برنامج الدعم الاجتماعي',
      category: 'community',
      type: 'photos',
      date: '2025-01-10',
      location: 'مناطق متعددة - العراق',
      photographer: 'سارة علي الموسوي',
      tags: ['مساعدات إنسانية', 'دعم اجتماعي', 'أسر محتاجة', 'برامج خيرية'],
      thumbnail: '/images/gallery/thumb3.jpg',
      fullImage: '/images/gallery/full3.jpg',
      views: 1675,
      likes: 298,
      downloads: 145,
      shares: 67,
      featured: false,
      resolution: '4K',
      fileSize: '3.1 MB',
      camera: 'Sony A7 III',
      settings: 'f/1.8, 1/200s, ISO 200'
    },
    {
      id: 4,
      title: 'مؤتمر التنمية المستدامة والاستثمار',
      description: 'صور من مشاركة النائب في مؤتمر التنمية المستدامة والاستثمار في دبي، والذي ناقش فرص الاستثمار في العراق والتنمية الاقتصادية المستدامة',
      category: 'conferences',
      type: 'photos',
      date: '2025-01-05',
      location: 'دبي - دولة الإمارات العربية المتحدة',
      photographer: 'محمد حسن البغدادي',
      tags: ['تنمية مستدامة', 'استثمار', 'دبي', 'مؤتمر دولي'],
      thumbnail: '/images/gallery/thumb4.jpg',
      fullImage: '/images/gallery/full4.jpg',
      views: 1520,
      likes: 187,
      downloads: 78,
      shares: 45,
      featured: true,
      resolution: '4K',
      fileSize: '2.8 MB',
      camera: 'Nikon D850',
      settings: 'f/2.5, 1/160s, ISO 320'
    },
    {
      id: 5,
      title: 'احتفالية يوم الشهيد العراقي',
      description: 'فيديو مؤثر من احتفالية يوم الشهيد العراقي، تكريماً لشهداء العراق الأبرار الذين ضحوا بأرواحهم من أجل الوطن والدفاع عن كرامة الشعب العراقي',
      category: 'ceremonies',
      type: 'videos',
      date: '2024-12-01',
      location: 'نصب الشهيد - بغداد',
      photographer: 'فريق الإعلام الحكومي',
      tags: ['يوم الشهيد', 'تكريم', 'شهداء العراق', 'احتفالية وطنية'],
      thumbnail: '/images/gallery/thumb5.jpg',
      videoUrl: '/videos/martyr-day.mp4',
      duration: '12:30',
      views: 3100,
      likes: 456,
      downloads: 123,
      shares: 234,
      featured: true,
      resolution: '4K',
      fileSize: '287 MB',
      format: 'MP4'
    },
    {
      id: 6,
      title: 'اجتماع لجنة الخدمات والإعمار',
      description: 'صور من اجتماع لجنة الخدمات والإعمار النيابية لمناقشة مشاريع البنية التحتية والخدمات الأساسية في المحافظات العراقية',
      category: 'meetings',
      type: 'photos',
      date: '2024-11-28',
      location: 'مجلس النواب العراقي - بغداد',
      photographer: 'علي حسين الناصري',
      tags: ['لجنة الخدمات', 'إعمار', 'بنية تحتية', 'خدمات أساسية'],
      thumbnail: '/images/gallery/thumb6.jpg',
      fullImage: '/images/gallery/full6.jpg',
      views: 1780,
      likes: 134,
      downloads: 56,
      shares: 23,
      featured: false,
      resolution: '4K',
      fileSize: '2.1 MB',
      camera: 'Canon EOS R6',
      settings: 'f/3.2, 1/100s, ISO 500'
    },
    {
      id: 7,
      title: 'افتتاح مستشفى الأطفال الجديد',
      description: 'فيديو من حفل افتتاح مستشفى الأطفال الجديد في بغداد، والذي يعد إضافة مهمة للقطاع الصحي ويوفر خدمات طبية متطورة للأطفال',
      category: 'projects',
      type: 'videos',
      date: '2024-11-20',
      location: 'بغداد - مستشفى الأطفال الجديد',
      photographer: 'فاطمة أحمد الزهراء',
      tags: ['مستشفى أطفال', 'افتتاح', 'قطاع صحي', 'خدمات طبية'],
      thumbnail: '/images/gallery/thumb7.jpg',
      videoUrl: '/videos/hospital-opening.mp4',
      duration: '15:20',
      views: 2890,
      likes: 378,
      downloads: 145,
      shares: 167,
      featured: true,
      resolution: '4K',
      fileSize: '342 MB',
      format: 'MP4'
    },
    {
      id: 8,
      title: 'ندوة التعليم الرقمي في العراق',
      description: 'صور من ندوة التعليم الرقمي في العراق والتي ناقشت استراتيجيات تطوير التعليم وإدخال التكنولوجيا الحديثة في المناهج الدراسية',
      category: 'education',
      type: 'photos',
      date: '2024-11-15',
      location: 'جامعة بغداد - كلية التربية',
      photographer: 'حسام الدين المالكي',
      tags: ['تعليم رقمي', 'تكنولوجيا', 'مناهج دراسية', 'ندوة تعليمية'],
      thumbnail: '/images/gallery/thumb8.jpg',
      fullImage: '/images/gallery/full8.jpg',
      views: 1456,
      likes: 189,
      downloads: 67,
      shares: 34,
      featured: false,
      resolution: '4K',
      fileSize: '2.7 MB',
      camera: 'Sony A7R IV',
      settings: 'f/2.0, 1/125s, ISO 250'
    },
    {
      id: 9,
      title: 'مهرجان التراث العراقي السنوي',
      description: 'مجموعة صور ملونة من مهرجان التراث العراقي السنوي الذي يحتفي بالثقافة والتراث العراقي الأصيل ويعرض الفنون الشعبية والحرف التقليدية',
      category: 'events',
      type: 'photos',
      date: '2024-11-10',
      location: 'ساحة التحرير - بغداد',
      photographer: 'نور الهدى الكربلائي',
      tags: ['تراث عراقي', 'مهرجان', 'ثقافة', 'فنون شعبية'],
      thumbnail: '/images/gallery/thumb9.jpg',
      fullImage: '/images/gallery/full9.jpg',
      views: 2234,
      likes: 345,
      downloads: 178,
      shares: 89,
      featured: true,
      resolution: '4K',
      fileSize: '3.4 MB',
      camera: 'Canon EOS R5',
      settings: 'f/1.4, 1/250s, ISO 100'
    },
    {
      id: 10,
      title: 'لقاء مع ممثلي المجتمع المدني',
      description: 'فيديو من اللقاء التشاوري مع ممثلي منظمات المجتمع المدني لمناقشة القضايا المجتمعية والتعاون في تنفيذ المشاريع التنموية',
      category: 'community',
      type: 'videos',
      date: '2024-11-05',
      location: 'مركز المؤتمرات - بغداد',
      photographer: 'عبد الرحمن الجبوري',
      tags: ['مجتمع مدني', 'لقاء تشاوري', 'تنمية مجتمعية', 'شراكة'],
      thumbnail: '/images/gallery/thumb10.jpg',
      videoUrl: '/videos/civil-society-meeting.mp4',
      duration: '18:45',
      views: 1567,
      likes: 198,
      downloads: 89,
      shares: 56,
      featured: false,
      resolution: '1080p',
      fileSize: '234 MB',
      format: 'MP4'
    },
    {
      id: 11,
      title: 'زيارة مصنع الأدوية الوطني',
      description: 'صور من الزيارة التفقدية لمصنع الأدوية الوطني في بغداد للاطلاع على خطوط الإنتاج ومناقشة سبل تطوير الصناعة الدوائية المحلية',
      category: 'visits',
      type: 'photos',
      date: '2024-10-28',
      location: 'مصنع الأدوية الوطني - بغداد',
      photographer: 'رنا صالح العبيدي',
      tags: ['صناعة دوائية', 'مصنع أدوية', 'زيارة تفقدية', 'إنتاج محلي'],
      thumbnail: '/images/gallery/thumb11.jpg',
      fullImage: '/images/gallery/full11.jpg',
      views: 1345,
      likes: 167,
      downloads: 45,
      shares: 28,
      featured: false,
      resolution: '4K',
      fileSize: '2.9 MB',
      camera: 'Nikon Z7 II',
      settings: 'f/2.8, 1/160s, ISO 400'
    },
    {
      id: 12,
      title: 'مؤتمر الاستثمار في الطاقة المتجددة',
      description: 'فيديو شامل من مؤتمر الاستثمار في الطاقة المتجددة الذي عقد في أربيل، والذي ناقش مشاريع الطاقة الشمسية وطاقة الرياح في العراق',
      category: 'conferences',
      type: 'videos',
      date: '2024-10-20',
      location: 'أربيل - مركز المؤتمرات الدولي',
      photographer: 'فريق الإعلام الإقليمي',
      tags: ['طاقة متجددة', 'استثمار', 'طاقة شمسية', 'أربيل'],
      thumbnail: '/images/gallery/thumb12.jpg',
      videoUrl: '/videos/renewable-energy-conference.mp4',
      duration: '22:15',
      views: 2678,
      likes: 289,
      downloads: 134,
      shares: 178,
      featured: true,
      resolution: '4K',
      fileSize: '456 MB',
      format: 'MP4'
    }
  ]

  // وظائف مساعدة متقدمة
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-IQ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getCategoryInfo = (categoryId) => {
    return categories.find(cat => cat.id === categoryId) || categories[0]
  }

  const getMediaTypeInfo = (typeId) => {
    return mediaTypes.find(type => type.id === typeId) || mediaTypes[0]
  }

  const filterByDateRange = (item) => {
    if (selectedDateRange === 'all') return true

    const itemDate = new Date(item.date)
    const now = new Date()

    switch (selectedDateRange) {
      case 'today':
        return itemDate.toDateString() === now.toDateString()
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        return itemDate >= weekAgo
      case 'month':
        return itemDate.getMonth() === now.getMonth() && itemDate.getFullYear() === now.getFullYear()
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3)
        const itemQuarter = Math.floor(itemDate.getMonth() / 3)
        return itemQuarter === quarter && itemDate.getFullYear() === now.getFullYear()
      case 'year':
        return itemDate.getFullYear() === now.getFullYear()
      default:
        return true
    }
  }

  const sortMedia = (media) => {
    const sorted = [...media]

    switch (sortBy) {
      case 'newest':
        return sorted.sort((a, b) => new Date(b.date) - new Date(a.date))
      case 'oldest':
        return sorted.sort((a, b) => new Date(a.date) - new Date(b.date))
      case 'most_viewed':
        return sorted.sort((a, b) => b.views - a.views)
      case 'most_liked':
        return sorted.sort((a, b) => b.likes - a.likes)
      case 'most_downloaded':
        return sorted.sort((a, b) => b.downloads - a.downloads)
      case 'alphabetical':
        return sorted.sort((a, b) => a.title.localeCompare(b.title, 'ar'))
      case 'category':
        return sorted.sort((a, b) => a.category.localeCompare(b.category))
      default:
        return sorted
    }
  }

  // تصفية وترتيب البيانات
  const getFilteredAndSortedMedia = () => {
    let filtered = galleryData.filter(item => {
      const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
                           item.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.photographer.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
      const matchesType = selectedType === 'all' || item.type === selectedType
      const matchesDate = filterByDateRange(item)

      return matchesSearch && matchesCategory && matchesType && matchesDate
    })

    return sortMedia(filtered)
  }

  const allMedia = getFilteredAndSortedMedia()
  const featuredMedia = allMedia.filter(item => item.featured)

  // منطق الصفحات
  const totalPages = Math.ceil(allMedia.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedMedia = allMedia.slice(startIndex, startIndex + itemsPerPage)

  // وظائف التفاعل
  const handleLike = (mediaId) => {
    setLikedMedia(prev =>
      prev.includes(mediaId)
        ? prev.filter(id => id !== mediaId)
        : [...prev, mediaId]
    )
  }

  const handleBookmark = (mediaId) => {
    setBookmarkedMedia(prev =>
      prev.includes(mediaId)
        ? prev.filter(id => id !== mediaId)
        : [...prev, mediaId]
    )
  }

  const handleShare = (media) => {
    if (navigator.share) {
      navigator.share({
        title: media.title,
        text: media.description,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      alert('تم نسخ الرابط إلى الحافظة')
    }
  }

  const openLightbox = (media, index) => {
    setSelectedMedia(media)
    setCurrentMediaIndex(index)
    setShowLightbox(true)
    setImageZoom(1)
    setImageRotation(0)
  }

  const closeLightbox = () => {
    setSelectedMedia(null)
    setShowLightbox(false)
    setCurrentMediaIndex(0)
    setImageZoom(1)
    setImageRotation(0)
  }

  const navigateMedia = (direction) => {
    const newIndex = direction === 'next'
      ? (currentMediaIndex + 1) % allMedia.length
      : (currentMediaIndex - 1 + allMedia.length) % allMedia.length

    setCurrentMediaIndex(newIndex)
    setSelectedMedia(allMedia[newIndex])
    setImageZoom(1)
    setImageRotation(0)
  }

  // إحصائيات متقدمة
  const totalViews = galleryData.reduce((sum, item) => sum + item.views, 0)
  const totalLikes = galleryData.reduce((sum, item) => sum + item.likes, 0)
  const totalDownloads = galleryData.reduce((sum, item) => sum + item.downloads, 0)
  const totalShares = galleryData.reduce((sum, item) => sum + item.shares, 0)
  const photosCount = galleryData.filter(item => item.type === 'photos').length
  const videosCount = galleryData.filter(item => item.type === 'videos').length

  // تأثيرات الرسوم المتحركة
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setIsVisible(prev => ({
            ...prev,
            [entry.target.id]: true
          }))
        }
      })
    }, observerOptions)

    const animatedElements = document.querySelectorAll('[data-animate]')
    animatedElements.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  // إضافة فئات CSS للرسوم المتحركة
  useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      [data-animate] {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease-out;
      }
      [data-animate].animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      .masonry-grid {
        column-count: 3;
        column-gap: 1.5rem;
        break-inside: avoid;
      }
      @media (max-width: 1024px) {
        .masonry-grid {
          column-count: 2;
        }
      }
      @media (max-width: 640px) {
        .masonry-grid {
          column-count: 1;
        }
      }
      .masonry-item {
        break-inside: avoid;
        margin-bottom: 1.5rem;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)
    return () => document.head.removeChild(style)
  }, [])

  // تطبيق الرسوم المتحركة
  useEffect(() => {
    Object.keys(isVisible).forEach(id => {
      if (isVisible[id]) {
        const element = document.getElementById(id)
        if (element) {
          element.classList.add('animate-in')
        }
      }
    })
  }, [isVisible])

  // تحديث العنوان عند تغيير الفلاتر
  useEffect(() => {
    const totalResults = allMedia.length
    const categoryName = selectedCategory === 'all' ? 'جميع الفئات' : getCategoryInfo(selectedCategory).name
    document.title = `معرض الصور والفيديوهات - ${categoryName} (${totalResults} عنصر) - موقع النائب العراقي`
  }, [selectedCategory, allMedia.length])

  // حفظ حالة الفلاتر في localStorage
  useEffect(() => {
    const filters = {
      searchTerm,
      selectedCategory,
      selectedType,
      selectedDateRange,
      sortBy,
      viewMode,
      currentPage
    }
    localStorage.setItem('galleryFilters', JSON.stringify(filters))
  }, [searchTerm, selectedCategory, selectedType, selectedDateRange, sortBy, viewMode, currentPage])

  // معالجة اختصارات لوحة المفاتيح
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (showLightbox) {
        switch (e.key) {
          case 'Escape':
            closeLightbox()
            break
          case 'ArrowLeft':
            navigateMedia('next')
            break
          case 'ArrowRight':
            navigateMedia('prev')
            break
          case 'i':
          case 'I':
            setShowMediaInfo(!showMediaInfo)
            break
          case '+':
          case '=':
            if (selectedMedia?.type === 'photos') {
              setImageZoom(prev => Math.min(prev + 0.25, 3))
            }
            break
          case '-':
            if (selectedMedia?.type === 'photos') {
              setImageZoom(prev => Math.max(prev - 0.25, 0.5))
            }
            break
          case 'r':
          case 'R':
            if (selectedMedia?.type === 'photos') {
              setImageRotation(prev => prev + 90)
            }
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [showLightbox, showMediaInfo, selectedMedia])

  // تحسين الأداء - تحديث النتائج فقط عند الحاجة
  useEffect(() => {
    setCurrentPage(1) // إعادة تعيين الصفحة عند تغيير الفلاتر
  }, [searchTerm, selectedCategory, selectedType, selectedDateRange, sortBy])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* قسم البطل مع الإحصائيات */}
      <section className="relative bg-gradient-to-r from-iraqi-blue via-blue-800 to-iraqi-blue text-white py-20 overflow-hidden">
        {/* خلفية زخرفية */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-10 w-32 h-32 border border-white rounded-full"></div>
          <div className="absolute bottom-10 left-10 w-24 h-24 border border-white rounded-full"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-white rounded-full"></div>
        </div>

        <div className="container-custom relative z-10">
          <div className="text-center mb-12" data-animate id="hero-content">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
              معرض الصور والفيديوهات
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              مجموعة شاملة من الصور والفيديوهات التي توثق أنشطة وفعاليات النائب العراقي والمشاريع التنموية والأنشطة المجتمعية
            </p>
          </div>

          {/* الإحصائيات المتقدمة */}
          <div className="grid grid-cols-2 md:grid-cols-6 gap-6 max-w-6xl mx-auto">
            <div className="text-center" data-animate id="stat-1">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Camera className="w-8 h-8 text-iraqi-gold" />
              </div>
              <div className="text-3xl font-bold text-iraqi-gold">{photosCount}</div>
              <div className="text-blue-100">صورة</div>
            </div>

            <div className="text-center" data-animate id="stat-2">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Film className="w-8 h-8 text-red-400" />
              </div>
              <div className="text-3xl font-bold text-red-400">{videosCount}</div>
              <div className="text-blue-100">فيديو</div>
            </div>

            <div className="text-center" data-animate id="stat-3">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Eye className="w-8 h-8 text-green-400" />
              </div>
              <div className="text-3xl font-bold text-green-400">{(totalViews / 1000).toFixed(1)}K</div>
              <div className="text-blue-100">مشاهدة</div>
            </div>

            <div className="text-center" data-animate id="stat-4">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Heart className="w-8 h-8 text-pink-400" />
              </div>
              <div className="text-3xl font-bold text-pink-400">{totalLikes}</div>
              <div className="text-blue-100">إعجاب</div>
            </div>

            <div className="text-center" data-animate id="stat-5">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Download className="w-8 h-8 text-iraqi-green" />
              </div>
              <div className="text-3xl font-bold text-iraqi-green">{totalDownloads}</div>
              <div className="text-blue-100">تحميل</div>
            </div>

            <div className="text-center" data-animate id="stat-6">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Share2 className="w-8 h-8 text-iraqi-red" />
              </div>
              <div className="text-3xl font-bold text-iraqi-red">{totalShares}</div>
              <div className="text-blue-100">مشاركة</div>
            </div>
          </div>
        </div>
      </section>

      <div className="container-custom py-12">

        {/* الوسائط المميزة */}
        {featuredMedia.length > 0 && (
          <section className="mb-16" data-animate id="featured-media">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">الوسائط المميزة</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                أهم الصور والفيديوهات التي توثق الأحداث والفعاليات المهمة
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredMedia.slice(0, 4).map((media, index) => {
                const categoryInfo = getCategoryInfo(media.category)
                const CategoryIcon = categoryInfo.icon

                return (
                  <div
                    key={media.id}
                    className="group relative bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    onClick={() => openLightbox(media, allMedia.findIndex(m => m.id === media.id))}
                  >
                    {/* صورة الوسائط */}
                    <div className="relative h-64 bg-gradient-to-br from-gray-200 to-gray-300 overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        {media.type === 'photos' ? (
                          <Camera className="w-16 h-16 text-white opacity-50" />
                        ) : (
                          <Film className="w-16 h-16 text-white opacity-50" />
                        )}
                      </div>

                      {/* شارات الحالة */}
                      <div className="absolute top-4 right-4 flex gap-2">
                        <span className="bg-iraqi-gold text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                          <Star className="w-3 h-3 ml-1" />
                          مميز
                        </span>
                        <span className={`${categoryInfo.color} text-white px-3 py-1 rounded-full text-sm font-medium flex items-center`}>
                          <CategoryIcon className="w-3 h-3 ml-1" />
                          {categoryInfo.name}
                        </span>
                      </div>

                      {/* مدة الفيديو */}
                      {media.type === 'videos' && media.duration && (
                        <div className="absolute bottom-4 left-4 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-sm">
                          {media.duration}
                        </div>
                      )}

                      {/* تراكب التفاعل */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          {media.type === 'photos' ? (
                            <div className="bg-white text-gray-900 p-4 rounded-full">
                              <ZoomIn className="w-6 h-6" />
                            </div>
                          ) : (
                            <div className="bg-white text-gray-900 p-4 rounded-full">
                              <Play className="w-6 h-6" />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* معلومات الوسائط */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center text-gray-500 text-sm">
                          <Calendar className="w-4 h-4 ml-1" />
                          {formatDate(media.date)}
                        </div>
                        <div className="flex items-center text-gray-500 text-sm">
                          <MapPin className="w-4 h-4 ml-1" />
                          {media.location.split(' - ')[0]}
                        </div>
                      </div>

                      <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                        {media.title}
                      </h3>

                      <p className="text-gray-600 mb-4 line-clamp-2">
                        {media.description}
                      </p>

                      {/* إحصائيات التفاعل */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div className="flex items-center gap-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleLike(media.id)
                            }}
                            className={`flex items-center px-3 py-1 rounded-lg transition-all duration-300 ${
                              likedMedia.includes(media.id)
                                ? 'bg-red-100 text-red-600'
                                : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                            }`}
                          >
                            <Heart className="w-4 h-4 ml-1" />
                            {media.likes}
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleBookmark(media.id)
                            }}
                            className={`flex items-center px-3 py-1 rounded-lg transition-all duration-300 ${
                              bookmarkedMedia.includes(media.id)
                                ? 'bg-iraqi-blue text-white'
                                : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                            }`}
                          >
                            <Bookmark className="w-4 h-4 ml-1" />
                            حفظ
                          </button>
                        </div>
                        <div className="flex items-center gap-3 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Eye className="w-4 h-4 ml-1" />
                            {media.views.toLocaleString()}
                          </div>
                          <div className="flex items-center">
                            <Download className="w-4 h-4 ml-1" />
                            {media.downloads}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </section>
        )}

        {/* نظام البحث والتصفية المتقدم */}
        <section className="mb-12" data-animate id="search-filters">
          <div className="bg-white rounded-xl shadow-lg p-6">
            {/* شريط البحث الرئيسي */}
            <div className="relative mb-6">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="ابحث في الصور والفيديوهات، الوصف، العلامات، المكان، أو المصور..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-12 pl-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent text-right text-lg"
              />
            </div>

            {/* فلاتر الفئات */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">الفئات</h3>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => {
                  const CategoryIcon = category.icon
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`flex items-center px-4 py-2 rounded-lg border transition-all duration-300 ${
                        selectedCategory === category.id
                          ? `${category.color} text-white border-transparent`
                          : 'bg-white text-gray-700 border-gray-300 hover:border-iraqi-blue hover:text-iraqi-blue'
                      }`}
                    >
                      <CategoryIcon className="w-4 h-4 ml-2" />
                      {category.name}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* فلاتر أنواع الوسائط */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">نوع الوسائط</h3>
              <div className="flex flex-wrap gap-2">
                {mediaTypes.map((type) => {
                  const TypeIcon = type.icon
                  return (
                    <button
                      key={type.id}
                      onClick={() => setSelectedType(type.id)}
                      className={`flex items-center px-4 py-2 rounded-lg border transition-all duration-300 ${
                        selectedType === type.id
                          ? 'bg-iraqi-blue text-white border-transparent'
                          : 'bg-white text-gray-700 border-gray-300 hover:border-iraqi-blue hover:text-iraqi-blue'
                      }`}
                    >
                      <TypeIcon className="w-4 h-4 ml-2" />
                      {type.name}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* فلاتر التاريخ والترتيب */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق التاريخ</label>
                <select
                  value={selectedDateRange}
                  onChange={(e) => setSelectedDateRange(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                >
                  {dateRanges.map((range) => (
                    <option key={range.id} value={range.id}>
                      {range.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ترتيب حسب</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                >
                  {sortOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">طريقة العرض</label>
                <div className="flex items-center gap-1 border border-gray-300 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`flex items-center px-3 py-1 rounded transition-all duration-300 ${
                      viewMode === 'grid'
                        ? 'bg-iraqi-blue text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Grid className="w-4 h-4 ml-1" />
                    شبكي
                  </button>
                  <button
                    onClick={() => setViewMode('masonry')}
                    className={`flex items-center px-3 py-1 rounded transition-all duration-300 ${
                      viewMode === 'masonry'
                        ? 'bg-iraqi-blue text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Settings className="w-4 h-4 ml-1" />
                    بناء
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`flex items-center px-3 py-1 rounded transition-all duration-300 ${
                      viewMode === 'list'
                        ? 'bg-iraqi-blue text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <List className="w-4 h-4 ml-1" />
                    قائمة
                  </button>
                </div>
              </div>
            </div>

            {/* معلومات النتائج */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  عرض {paginatedMedia.length} من {allMedia.length} عنصر
                </div>
                <button
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCategory('all')
                    setSelectedType('all')
                    setSelectedDateRange('all')
                    setSortBy('newest')
                    setCurrentPage(1)
                  }}
                  className="text-sm text-iraqi-blue hover:text-iraqi-blue/80 transition-colors"
                >
                  إعادة تعيين الفلاتر
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* عرض الوسائط */}
        <section data-animate id="media-display">
          {/* العرض الشبكي */}
          {viewMode === 'grid' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {paginatedMedia.map((media, index) => {
                const categoryInfo = getCategoryInfo(media.category)
                const CategoryIcon = categoryInfo.icon

                return (
                  <div
                    key={media.id}
                    className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    onClick={() => openLightbox(media, allMedia.findIndex(m => m.id === media.id))}
                  >
                    {/* صورة الوسائط */}
                    <div className="relative h-48 bg-gradient-to-br from-gray-200 to-gray-300 overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        {media.type === 'photos' ? (
                          <Camera className="w-12 h-12 text-white opacity-50" />
                        ) : (
                          <Film className="w-12 h-12 text-white opacity-50" />
                        )}
                      </div>

                      {/* شارات الحالة */}
                      <div className="absolute top-3 right-3 flex gap-1">
                        {media.featured && (
                          <span className="bg-iraqi-gold text-white px-2 py-1 rounded-full text-xs font-medium">
                            <Star className="w-3 h-3 inline ml-1" />
                            مميز
                          </span>
                        )}
                        <span className={`${categoryInfo.color} text-white px-2 py-1 rounded-full text-xs font-medium`}>
                          <CategoryIcon className="w-3 h-3 inline ml-1" />
                          {categoryInfo.name.split(' ')[0]}
                        </span>
                      </div>

                      {/* مدة الفيديو */}
                      {media.type === 'videos' && media.duration && (
                        <div className="absolute bottom-3 left-3 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                          {media.duration}
                        </div>
                      )}

                      {/* تراكب التفاعل */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex gap-2">
                          <div className="bg-white text-gray-900 p-2 rounded-full">
                            {media.type === 'photos' ? (
                              <ZoomIn className="w-4 h-4" />
                            ) : (
                              <Play className="w-4 h-4" />
                            )}
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleShare(media)
                            }}
                            className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100 transition-colors"
                          >
                            <Share2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* معلومات الوسائط */}
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center text-gray-500 text-xs">
                          <Calendar className="w-3 h-3 ml-1" />
                          {formatDate(media.date)}
                        </div>
                        <div className="flex items-center text-gray-500 text-xs">
                          <User className="w-3 h-3 ml-1" />
                          {media.photographer.split(' ')[0]}
                        </div>
                      </div>

                      <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2">
                        {media.title}
                      </h3>

                      <p className="text-gray-600 text-xs mb-3 line-clamp-2">
                        {media.description}
                      </p>

                      {/* إحصائيات التفاعل */}
                      <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleLike(media.id)
                            }}
                            className={`p-1 rounded transition-all duration-300 ${
                              likedMedia.includes(media.id)
                                ? 'bg-red-100 text-red-600'
                                : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                            }`}
                          >
                            <Heart className="w-3 h-3" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleBookmark(media.id)
                            }}
                            className={`p-1 rounded transition-all duration-300 ${
                              bookmarkedMedia.includes(media.id)
                                ? 'bg-iraqi-blue text-white'
                                : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                            }`}
                          >
                            <Bookmark className="w-3 h-3" />
                          </button>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-gray-500">
                          <div className="flex items-center">
                            <Eye className="w-3 h-3 ml-1" />
                            {media.views > 1000 ? `${(media.views / 1000).toFixed(1)}K` : media.views}
                          </div>
                          <div className="flex items-center">
                            <Download className="w-3 h-3 ml-1" />
                            {media.downloads}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}

          {/* العرض البنائي (Masonry) */}
          {viewMode === 'masonry' && (
            <div className="masonry-grid">
              {paginatedMedia.map((media, index) => {
                const categoryInfo = getCategoryInfo(media.category)
                const CategoryIcon = categoryInfo.icon
                const randomHeight = Math.floor(Math.random() * 200) + 200 // ارتفاع عشوائي للتأثير البنائي

                return (
                  <div
                    key={media.id}
                    className="masonry-item group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    onClick={() => openLightbox(media, allMedia.findIndex(m => m.id === media.id))}
                  >
                    {/* صورة الوسائط */}
                    <div
                      className="relative bg-gradient-to-br from-gray-200 to-gray-300 overflow-hidden"
                      style={{ height: `${randomHeight}px` }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        {media.type === 'photos' ? (
                          <Camera className="w-12 h-12 text-white opacity-50" />
                        ) : (
                          <Film className="w-12 h-12 text-white opacity-50" />
                        )}
                      </div>

                      {/* شارات الحالة */}
                      <div className="absolute top-3 right-3 flex flex-col gap-1">
                        {media.featured && (
                          <span className="bg-iraqi-gold text-white px-2 py-1 rounded-full text-xs font-medium">
                            <Star className="w-3 h-3 inline ml-1" />
                            مميز
                          </span>
                        )}
                        <span className={`${categoryInfo.color} text-white px-2 py-1 rounded-full text-xs font-medium`}>
                          <CategoryIcon className="w-3 h-3 inline ml-1" />
                          {categoryInfo.name.split(' ')[0]}
                        </span>
                      </div>

                      {/* مدة الفيديو */}
                      {media.type === 'videos' && media.duration && (
                        <div className="absolute bottom-3 left-3 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                          {media.duration}
                        </div>
                      )}

                      {/* تراكب التفاعل */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-white text-gray-900 p-3 rounded-full">
                            {media.type === 'photos' ? (
                              <ZoomIn className="w-5 h-5" />
                            ) : (
                              <Play className="w-5 h-5" />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* معلومات الوسائط */}
                    <div className="p-4">
                      <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2">
                        {media.title}
                      </h3>

                      <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 ml-1" />
                          {formatDate(media.date)}
                        </div>
                        <div className="flex items-center">
                          <Eye className="w-3 h-3 ml-1" />
                          {media.views > 1000 ? `${(media.views / 1000).toFixed(1)}K` : media.views}
                        </div>
                      </div>

                      {/* أزرار التفاعل */}
                      <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                        <div className="flex items-center gap-1">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleLike(media.id)
                            }}
                            className={`p-1 rounded transition-all duration-300 ${
                              likedMedia.includes(media.id)
                                ? 'bg-red-100 text-red-600'
                                : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                            }`}
                          >
                            <Heart className="w-3 h-3" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleBookmark(media.id)
                            }}
                            className={`p-1 rounded transition-all duration-300 ${
                              bookmarkedMedia.includes(media.id)
                                ? 'bg-iraqi-blue text-white'
                                : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                            }`}
                          >
                            <Bookmark className="w-3 h-3" />
                          </button>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleShare(media)
                          }}
                          className="p-1 rounded bg-gray-100 text-gray-600 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                        >
                          <Share2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}

          {/* العرض القائمي */}
          {viewMode === 'list' && (
            <div className="space-y-6">
              {paginatedMedia.map((media, index) => {
                const categoryInfo = getCategoryInfo(media.category)
                const CategoryIcon = categoryInfo.icon

                return (
                  <div
                    key={media.id}
                    className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    onClick={() => openLightbox(media, allMedia.findIndex(m => m.id === media.id))}
                  >
                    <div className="flex flex-col md:flex-row">
                      {/* صورة الوسائط */}
                      <div className="relative w-full md:w-80 h-48 bg-gradient-to-br from-gray-200 to-gray-300 overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          {media.type === 'photos' ? (
                            <Camera className="w-12 h-12 text-white opacity-50" />
                          ) : (
                            <Film className="w-12 h-12 text-white opacity-50" />
                          )}
                        </div>

                        {/* شارات الحالة */}
                        <div className="absolute top-3 right-3 flex gap-1">
                          {media.featured && (
                            <span className="bg-iraqi-gold text-white px-2 py-1 rounded-full text-xs font-medium">
                              <Star className="w-3 h-3 inline ml-1" />
                              مميز
                            </span>
                          )}
                          <span className={`${categoryInfo.color} text-white px-2 py-1 rounded-full text-xs font-medium`}>
                            <CategoryIcon className="w-3 h-3 inline ml-1" />
                            {categoryInfo.name}
                          </span>
                        </div>

                        {/* مدة الفيديو */}
                        {media.type === 'videos' && media.duration && (
                          <div className="absolute bottom-3 left-3 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                            {media.duration}
                          </div>
                        )}

                        {/* تراكب التفاعل */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="bg-white text-gray-900 p-3 rounded-full">
                              {media.type === 'photos' ? (
                                <ZoomIn className="w-5 h-5" />
                              ) : (
                                <Play className="w-5 h-5" />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* معلومات الوسائط */}
                      <div className="flex-1 p-6">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center text-gray-500 text-sm">
                            <Calendar className="w-4 h-4 ml-1" />
                            {formatDate(media.date)}
                          </div>
                          <div className="flex items-center text-gray-500 text-sm">
                            <MapPin className="w-4 h-4 ml-1" />
                            {media.location.split(' - ')[0]}
                          </div>
                        </div>

                        <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                          {media.title}
                        </h3>

                        <p className="text-gray-600 mb-4 line-clamp-3">
                          {media.description}
                        </p>

                        {/* العلامات */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {media.tags.slice(0, 4).map((tag, tagIndex) => (
                            <span
                              key={tagIndex}
                              className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs"
                            >
                              <Tag className="w-3 h-3 inline ml-1" />
                              {tag}
                            </span>
                          ))}
                          {media.tags.length > 4 && (
                            <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                              +{media.tags.length - 4} المزيد
                            </span>
                          )}
                        </div>

                        {/* معلومات المصور والتفاصيل التقنية */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 text-sm text-gray-600">
                          <div className="flex items-center">
                            <User className="w-4 h-4 ml-1" />
                            المصور: {media.photographer}
                          </div>
                          <div className="flex items-center">
                            <Info className="w-4 h-4 ml-1" />
                            الدقة: {media.resolution}
                          </div>
                          {media.camera && (
                            <div className="flex items-center">
                              <Camera className="w-4 h-4 ml-1" />
                              الكاميرا: {media.camera}
                            </div>
                          )}
                          <div className="flex items-center">
                            <FileText className="w-4 h-4 ml-1" />
                            الحجم: {media.fileSize}
                          </div>
                        </div>

                        {/* إحصائيات التفاعل */}
                        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                          <div className="flex items-center gap-3">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleLike(media.id)
                              }}
                              className={`flex items-center px-3 py-2 rounded-lg transition-all duration-300 ${
                                likedMedia.includes(media.id)
                                  ? 'bg-red-100 text-red-600'
                                  : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                              }`}
                            >
                              <Heart className="w-4 h-4 ml-1" />
                              {media.likes}
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleBookmark(media.id)
                              }}
                              className={`flex items-center px-3 py-2 rounded-lg transition-all duration-300 ${
                                bookmarkedMedia.includes(media.id)
                                  ? 'bg-iraqi-blue text-white'
                                  : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                              }`}
                            >
                              <Bookmark className="w-4 h-4 ml-1" />
                              حفظ
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleShare(media)
                              }}
                              className="flex items-center px-3 py-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                            >
                              <Share2 className="w-4 h-4 ml-1" />
                              مشاركة
                            </button>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Eye className="w-4 h-4 ml-1" />
                              {media.views.toLocaleString()} مشاهدة
                            </div>
                            <div className="flex items-center">
                              <Download className="w-4 h-4 ml-1" />
                              {media.downloads} تحميل
                            </div>
                            <div className="flex items-center">
                              <Share2 className="w-4 h-4 ml-1" />
                              {media.shares} مشاركة
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}

          {/* رسالة عدم وجود نتائج */}
          {allMedia.length === 0 && (
            <div className="text-center py-16" data-animate id="empty-state">
              <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-8 flex items-center justify-center">
                <ImageIcon className="w-16 h-16 text-gray-400" />
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">لا توجد نتائج</h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                لم يتم العثور على أي محتوى يطابق معايير البحث الخاصة بك. جرب تعديل الفلاتر أو البحث بكلمات مختلفة.
              </p>
              <button
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('all')
                  setSelectedType('all')
                  setSelectedDateRange('all')
                  setSortBy('newest')
                  setCurrentPage(1)
                }}
                className="bg-iraqi-blue text-white px-6 py-3 rounded-lg hover:bg-iraqi-blue/90 transition-colors"
              >
                إعادة تعيين الفلاتر
              </button>
            </div>
          )}
        </section>

        {/* نظام الصفحات */}
        {totalPages > 1 && (
          <section className="mt-12" data-animate id="pagination">
            <div className="flex items-center justify-center">
              <div className="flex items-center gap-2">
                {/* الصفحة السابقة */}
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                    currentPage === 1
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-iraqi-blue hover:text-white shadow-md'
                  }`}
                >
                  <ChevronRight className="w-4 h-4 ml-1" />
                  السابق
                </button>

                {/* أرقام الصفحات */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                    let pageNumber
                    if (totalPages <= 7) {
                      pageNumber = i + 1
                    } else if (currentPage <= 4) {
                      pageNumber = i + 1
                    } else if (currentPage >= totalPages - 3) {
                      pageNumber = totalPages - 6 + i
                    } else {
                      pageNumber = currentPage - 3 + i
                    }

                    return (
                      <button
                        key={pageNumber}
                        onClick={() => setCurrentPage(pageNumber)}
                        className={`w-10 h-10 rounded-lg transition-all duration-300 ${
                          currentPage === pageNumber
                            ? 'bg-iraqi-blue text-white shadow-lg'
                            : 'bg-white text-gray-700 hover:bg-iraqi-blue hover:text-white shadow-md'
                        }`}
                      >
                        {pageNumber}
                      </button>
                    )
                  })}
                </div>

                {/* الصفحة التالية */}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                    currentPage === totalPages
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-white text-gray-700 hover:bg-iraqi-blue hover:text-white shadow-md'
                  }`}
                >
                  التالي
                  <ChevronLeft className="w-4 h-4 mr-1" />
                </button>
              </div>
            </div>

            {/* معلومات الصفحة */}
            <div className="text-center mt-4 text-sm text-gray-600">
              عرض {startIndex + 1} - {Math.min(startIndex + itemsPerPage, allMedia.length)} من {allMedia.length} عنصر
            </div>
          </section>
        )}

        {/* مودال عرض الوسائط المتقدم */}
        {showLightbox && selectedMedia && (
          <div className="fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center">
            {/* شريط التحكم العلوي */}
            <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/50 to-transparent p-6 z-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button
                    onClick={closeLightbox}
                    className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-300"
                  >
                    <X className="w-6 h-6" />
                  </button>
                  <div className="text-white">
                    <h3 className="text-lg font-semibold">{selectedMedia.title}</h3>
                    <p className="text-sm text-gray-300">
                      {currentMediaIndex + 1} من {allMedia.length}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {/* أزرار التحكم في الصورة */}
                  {selectedMedia.type === 'photos' && (
                    <>
                      <button
                        onClick={() => setImageZoom(prev => Math.min(prev + 0.25, 3))}
                        className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-300"
                      >
                        <ZoomIn className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => setImageZoom(prev => Math.max(prev - 0.25, 0.5))}
                        className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-300"
                      >
                        <ZoomOut className="w-5 h-5" />
                      </button>
                      <button
                        onClick={() => setImageRotation(prev => prev + 90)}
                        className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-300"
                      >
                        <RotateCw className="w-5 h-5" />
                      </button>
                    </>
                  )}

                  <button
                    onClick={() => setShowMediaInfo(!showMediaInfo)}
                    className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-300"
                  >
                    <Info className="w-5 h-5" />
                  </button>

                  <button
                    onClick={() => handleShare(selectedMedia)}
                    className="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all duration-300"
                  >
                    <Share2 className="w-5 h-5" />
                  </button>

                  <button className="bg-iraqi-green hover:bg-iraqi-green/80 text-white px-4 py-2 rounded-lg transition-all duration-300">
                    <Download className="w-4 h-4 inline ml-1" />
                    تحميل
                  </button>
                </div>
              </div>
            </div>

            {/* المحتوى الرئيسي */}
            <div className="relative w-full h-full flex items-center justify-center p-20">
              {/* أزرار التنقل */}
              {allMedia.length > 1 && (
                <>
                  <button
                    onClick={() => navigateMedia('prev')}
                    className="absolute left-6 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300 z-10"
                  >
                    <ChevronRight className="w-6 h-6" />
                  </button>
                  <button
                    onClick={() => navigateMedia('next')}
                    className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-3 rounded-full transition-all duration-300 z-10"
                  >
                    <ChevronLeft className="w-6 h-6" />
                  </button>
                </>
              )}

              {/* عرض الوسائط */}
              <div className="relative max-w-6xl max-h-full flex items-center justify-center">
                {selectedMedia.type === 'photos' ? (
                  <div
                    className="relative bg-white rounded-lg overflow-hidden shadow-2xl cursor-move"
                    style={{
                      transform: `scale(${imageZoom}) rotate(${imageRotation}deg)`,
                      transition: 'transform 0.3s ease'
                    }}
                  >
                    <div className="w-full max-w-4xl h-auto bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center min-h-96">
                      <Camera className="w-24 h-24 text-gray-500" />
                    </div>
                  </div>
                ) : (
                  <div className="relative bg-black rounded-lg overflow-hidden shadow-2xl">
                    <div className="w-full max-w-4xl h-auto bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center min-h-96">
                      <div className="text-center text-white">
                        <Film className="w-24 h-24 mx-auto mb-4 text-gray-400" />
                        <p className="text-lg">فيديو: {selectedMedia.duration}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* لوحة المعلومات الجانبية */}
            {showMediaInfo && (
              <div className="absolute left-0 top-0 bottom-0 w-96 bg-white shadow-2xl overflow-y-auto z-20">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-bold text-gray-900">تفاصيل الوسائط</h3>
                    <button
                      onClick={() => setShowMediaInfo(false)}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>

                  {/* معلومات أساسية */}
                  <div className="space-y-4 mb-6">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">العنوان</h4>
                      <p className="text-gray-700">{selectedMedia.title}</p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">الوصف</h4>
                      <p className="text-gray-700 text-sm leading-relaxed">{selectedMedia.description}</p>
                    </div>
                  </div>

                  {/* معلومات تقنية */}
                  <div className="border-t border-gray-200 pt-6 mb-6">
                    <h4 className="font-semibold text-gray-900 mb-4">المعلومات التقنية</h4>
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">النوع:</span>
                        <span className="text-gray-900">{selectedMedia.type === 'photos' ? 'صورة' : 'فيديو'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">الدقة:</span>
                        <span className="text-gray-900">{selectedMedia.resolution}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">حجم الملف:</span>
                        <span className="text-gray-900">{selectedMedia.fileSize}</span>
                      </div>
                      {selectedMedia.camera && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">الكاميرا:</span>
                          <span className="text-gray-900">{selectedMedia.camera}</span>
                        </div>
                      )}
                      {selectedMedia.settings && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">الإعدادات:</span>
                          <span className="text-gray-900">{selectedMedia.settings}</span>
                        </div>
                      )}
                      {selectedMedia.duration && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">المدة:</span>
                          <span className="text-gray-900">{selectedMedia.duration}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* معلومات إضافية */}
                  <div className="border-t border-gray-200 pt-6 mb-6">
                    <h4 className="font-semibold text-gray-900 mb-4">معلومات إضافية</h4>
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">التاريخ:</span>
                        <span className="text-gray-900">{formatDate(selectedMedia.date)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">المكان:</span>
                        <span className="text-gray-900">{selectedMedia.location}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">المصور:</span>
                        <span className="text-gray-900">{selectedMedia.photographer}</span>
                      </div>
                    </div>
                  </div>

                  {/* العلامات */}
                  <div className="border-t border-gray-200 pt-6 mb-6">
                    <h4 className="font-semibold text-gray-900 mb-4">العلامات</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedMedia.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* إحصائيات */}
                  <div className="border-t border-gray-200 pt-6">
                    <h4 className="font-semibold text-gray-900 mb-4">الإحصائيات</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-gray-900">{selectedMedia.views.toLocaleString()}</div>
                        <div className="text-gray-600">مشاهدة</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-gray-900">{selectedMedia.likes}</div>
                        <div className="text-gray-600">إعجاب</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-gray-900">{selectedMedia.downloads}</div>
                        <div className="text-gray-600">تحميل</div>
                      </div>
                      <div className="text-center p-3 bg-gray-50 rounded-lg">
                        <div className="text-lg font-bold text-gray-900">{selectedMedia.shares}</div>
                        <div className="text-gray-600">مشاركة</div>
                      </div>
                    </div>
                  </div>

                  {/* أزرار التفاعل */}
                  <div className="border-t border-gray-200 pt-6 mt-6">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleLike(selectedMedia.id)}
                        className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg transition-all duration-300 ${
                          likedMedia.includes(selectedMedia.id)
                            ? 'bg-red-100 text-red-600'
                            : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                        }`}
                      >
                        <Heart className="w-4 h-4 ml-1" />
                        إعجاب
                      </button>
                      <button
                        onClick={() => handleBookmark(selectedMedia.id)}
                        className={`flex-1 flex items-center justify-center px-4 py-2 rounded-lg transition-all duration-300 ${
                          bookmarkedMedia.includes(selectedMedia.id)
                            ? 'bg-iraqi-blue text-white'
                            : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                        }`}
                      >
                        <Bookmark className="w-4 h-4 ml-1" />
                        حفظ
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* CSS للتخطيط البنائي */}
      <style jsx>{`
        .masonry-grid {
          column-count: 1;
          column-gap: 1.5rem;
          break-inside: avoid;
        }

        @media (min-width: 640px) {
          .masonry-grid {
            column-count: 2;
          }
        }

        @media (min-width: 768px) {
          .masonry-grid {
            column-count: 3;
          }
        }

        @media (min-width: 1024px) {
          .masonry-grid {
            column-count: 4;
          }
        }

        @media (min-width: 1280px) {
          .masonry-grid {
            column-count: 5;
          }
        }

        .masonry-item {
          break-inside: avoid;
          margin-bottom: 1.5rem;
          display: inline-block;
          width: 100%;
        }

        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* تحسينات الرسوم المتحركة */
        [data-animate] {
          opacity: 0;
          transform: translateY(30px);
          transition: all 0.6s ease-out;
        }

        [data-animate].animate-in {
          opacity: 1;
          transform: translateY(0);
        }

        /* تأثيرات التمرير */
        .hover-scale {
          transition: transform 0.3s ease;
        }

        .hover-scale:hover {
          transform: scale(1.02);
        }

        /* تحسينات الشبكة المتجاوبة */
        .container-custom {
          max-width: 1400px;
          margin: 0 auto;
          padding: 0 1rem;
        }

        @media (min-width: 640px) {
          .container-custom {
            padding: 0 2rem;
          }
        }

        /* تحسينات الألوان العراقية */
        .iraqi-gradient {
          background: linear-gradient(135deg, #1e40af 0%, #dc2626 25%, #059669 50%, #d97706 75%, #1e40af 100%);
          background-size: 400% 400%;
          animation: gradient-shift 8s ease infinite;
        }

        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }

        /* تحسينات الظلال */
        .shadow-iraqi {
          box-shadow: 0 10px 25px -3px rgba(30, 64, 175, 0.1), 0 4px 6px -2px rgba(30, 64, 175, 0.05);
        }

        .shadow-iraqi-lg {
          box-shadow: 0 20px 25px -5px rgba(30, 64, 175, 0.1), 0 10px 10px -5px rgba(30, 64, 175, 0.04);
        }

        /* تحسينات التركيز للوصولية */
        .focus-iraqi:focus {
          outline: 2px solid #1e40af;
          outline-offset: 2px;
        }

        /* تحسينات الحالة المحملة */
        .loading-shimmer {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }

        /* تحسينات الطباعة */
        @media print {
          .no-print {
            display: none !important;
          }

          .print-break {
            page-break-after: always;
          }
        }
      `}</style>
    </div>
  )
}

export default Gallery
