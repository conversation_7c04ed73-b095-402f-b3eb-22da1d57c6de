import React, { useState } from 'react'
import { 
  Save, 
  Settings as SettingsIcon,
  Globe,
  Shield,
  Bell,
  Palette,
  Database,
  Mail,
  Key,
  Upload,
  Download,
  Trash2,
  AlertTriangle
} from 'lucide-react'
import { Button, Input, Card, Switch, Alert, Textarea, Select } from '../../components/UI'
import { PageHeader } from '../../components/Common'

const AdminSettings = () => {
  const [showSuccessAlert, setShowSuccessAlert] = useState(false)
  const [activeTab, setActiveTab] = useState('general')

  // إعدادات عامة
  const [generalSettings, setGeneralSettings] = useState({
    siteName: 'موقع النائب محمد الخالدي',
    siteDescription: 'الموقع الرسمي للنائب محمد أحمد الخالدي - عضو مجلس النواب العراقي',
    siteKeywords: 'نائب، مجلس النواب، العراق، بغداد، خدمات',
    contactEmail: '<EMAIL>',
    contactPhone: '+964 ************',
    address: 'بغداد، العراق',
    timezone: 'Asia/Baghdad',
    language: 'ar',
    maintenanceMode: false
  })

  // إعدادات الأمان
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginAttempts: 5,
    ipWhitelist: '',
    sslRequired: true,
    backupFrequency: 'daily'
  })

  // إعدادات الإشعارات
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    newMessageAlert: true,
    systemUpdates: true,
    securityAlerts: true,
    weeklyReports: false,
    maintenanceAlerts: true
  })

  // إعدادات المظهر
  const [appearanceSettings, setAppearanceSettings] = useState({
    primaryColor: '#1e40af',
    secondaryColor: '#fbbf24',
    darkMode: false,
    rtlSupport: true,
    fontFamily: 'Cairo',
    logoUrl: '/images/logo.png',
    faviconUrl: '/images/favicon.ico'
  })

  const tabs = [
    { id: 'general', label: 'إعدادات عامة', icon: SettingsIcon },
    { id: 'security', label: 'الأمان', icon: Shield },
    { id: 'notifications', label: 'الإشعارات', icon: Bell },
    { id: 'appearance', label: 'المظهر', icon: Palette },
    { id: 'backup', label: 'النسخ الاحتياطي', icon: Database }
  ]

  const timezones = [
    { value: 'Asia/Baghdad', label: 'بغداد (GMT+3)' },
    { value: 'Asia/Riyadh', label: 'الرياض (GMT+3)' },
    { value: 'Europe/London', label: 'لندن (GMT+0)' },
    { value: 'America/New_York', label: 'نيويورك (GMT-5)' }
  ]

  const languages = [
    { value: 'ar', label: 'العربية' },
    { value: 'en', label: 'English' },
    { value: 'ku', label: 'کوردی' }
  ]

  const backupFrequencies = [
    { value: 'daily', label: 'يومياً' },
    { value: 'weekly', label: 'أسبوعياً' },
    { value: 'monthly', label: 'شهرياً' }
  ]

  const handleSaveSettings = () => {
    // هنا يتم حفظ الإعدادات
    setShowSuccessAlert(true)
    setTimeout(() => setShowSuccessAlert(false), 3000)
  }

  const handleExportData = () => {
    // تصدير البيانات
    console.log('Exporting data...')
  }

  const handleImportData = () => {
    // استيراد البيانات
    console.log('Importing data...')
  }

  const handleCreateBackup = () => {
    // إنشاء نسخة احتياطية
    console.log('Creating backup...')
  }

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <Card>
        <Card.Header>
          <Card.Title>معلومات الموقع</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <Input
            label="اسم الموقع"
            value={generalSettings.siteName}
            onChange={(e) => setGeneralSettings(prev => ({ ...prev, siteName: e.target.value }))}
          />
          <Textarea
            label="وصف الموقع"
            value={generalSettings.siteDescription}
            onChange={(e) => setGeneralSettings(prev => ({ ...prev, siteDescription: e.target.value }))}
            rows={3}
          />
          <Input
            label="الكلمات المفتاحية"
            value={generalSettings.siteKeywords}
            onChange={(e) => setGeneralSettings(prev => ({ ...prev, siteKeywords: e.target.value }))}
            placeholder="كلمة1، كلمة2، كلمة3"
          />
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>معلومات الاتصال</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <Input
            label="البريد الإلكتروني"
            type="email"
            value={generalSettings.contactEmail}
            onChange={(e) => setGeneralSettings(prev => ({ ...prev, contactEmail: e.target.value }))}
          />
          <Input
            label="رقم الهاتف"
            value={generalSettings.contactPhone}
            onChange={(e) => setGeneralSettings(prev => ({ ...prev, contactPhone: e.target.value }))}
          />
          <Input
            label="العنوان"
            value={generalSettings.address}
            onChange={(e) => setGeneralSettings(prev => ({ ...prev, address: e.target.value }))}
          />
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>إعدادات النظام</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <Select
            label="المنطقة الزمنية"
            value={generalSettings.timezone}
            onChange={(value) => setGeneralSettings(prev => ({ ...prev, timezone: value }))}
            options={timezones}
          />
          <Select
            label="اللغة الافتراضية"
            value={generalSettings.language}
            onChange={(value) => setGeneralSettings(prev => ({ ...prev, language: value }))}
            options={languages}
          />
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">وضع الصيانة</label>
              <p className="text-sm text-gray-500">تعطيل الموقع مؤقتاً للصيانة</p>
            </div>
            <Switch
              checked={generalSettings.maintenanceMode}
              onChange={(checked) => setGeneralSettings(prev => ({ ...prev, maintenanceMode: checked }))}
            />
          </div>
        </Card.Content>
      </Card>
    </div>
  )

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <Card>
        <Card.Header>
          <Card.Title>إعدادات المصادقة</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">المصادقة الثنائية</label>
              <p className="text-sm text-gray-500">تفعيل المصادقة الثنائية لحماية إضافية</p>
            </div>
            <Switch
              checked={securitySettings.twoFactorAuth}
              onChange={(checked) => setSecuritySettings(prev => ({ ...prev, twoFactorAuth: checked }))}
            />
          </div>
          
          <Input
            label="مهلة انتهاء الجلسة (بالدقائق)"
            type="number"
            value={securitySettings.sessionTimeout}
            onChange={(e) => setSecuritySettings(prev => ({ ...prev, sessionTimeout: parseInt(e.target.value) }))}
          />
          
          <Input
            label="انتهاء صلاحية كلمة المرور (بالأيام)"
            type="number"
            value={securitySettings.passwordExpiry}
            onChange={(e) => setSecuritySettings(prev => ({ ...prev, passwordExpiry: parseInt(e.target.value) }))}
          />
          
          <Input
            label="عدد محاولات تسجيل الدخول المسموحة"
            type="number"
            value={securitySettings.loginAttempts}
            onChange={(e) => setSecuritySettings(prev => ({ ...prev, loginAttempts: parseInt(e.target.value) }))}
          />
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>إعدادات الشبكة</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">إجبار استخدام SSL</label>
              <p className="text-sm text-gray-500">إعادة توجيه جميع الطلبات إلى HTTPS</p>
            </div>
            <Switch
              checked={securitySettings.sslRequired}
              onChange={(checked) => setSecuritySettings(prev => ({ ...prev, sslRequired: checked }))}
            />
          </div>
          
          <Textarea
            label="قائمة IP المسموحة"
            value={securitySettings.ipWhitelist}
            onChange={(e) => setSecuritySettings(prev => ({ ...prev, ipWhitelist: e.target.value }))}
            placeholder="***********&#10;********"
            rows={4}
          />
        </Card.Content>
      </Card>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <Card>
        <Card.Header>
          <Card.Title>إعدادات الإشعارات</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          {Object.entries({
            emailNotifications: 'إشعارات البريد الإلكتروني',
            newMessageAlert: 'تنبيهات الرسائل الجديدة',
            systemUpdates: 'تحديثات النظام',
            securityAlerts: 'تنبيهات الأمان',
            weeklyReports: 'التقارير الأسبوعية',
            maintenanceAlerts: 'تنبيهات الصيانة'
          }).map(([key, label]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-700">{label}</label>
              </div>
              <Switch
                checked={notificationSettings[key]}
                onChange={(checked) => setNotificationSettings(prev => ({ ...prev, [key]: checked }))}
              />
            </div>
          ))}
        </Card.Content>
      </Card>
    </div>
  )

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <Card>
        <Card.Header>
          <Card.Title>إعدادات المظهر</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="اللون الأساسي"
              type="color"
              value={appearanceSettings.primaryColor}
              onChange={(e) => setAppearanceSettings(prev => ({ ...prev, primaryColor: e.target.value }))}
            />
            <Input
              label="اللون الثانوي"
              type="color"
              value={appearanceSettings.secondaryColor}
              onChange={(e) => setAppearanceSettings(prev => ({ ...prev, secondaryColor: e.target.value }))}
            />
          </div>
          
          <Input
            label="خط النص"
            value={appearanceSettings.fontFamily}
            onChange={(e) => setAppearanceSettings(prev => ({ ...prev, fontFamily: e.target.value }))}
          />
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">الوضع المظلم</label>
              <p className="text-sm text-gray-500">تفعيل المظهر المظلم</p>
            </div>
            <Switch
              checked={appearanceSettings.darkMode}
              onChange={(checked) => setAppearanceSettings(prev => ({ ...prev, darkMode: checked }))}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">دعم RTL</label>
              <p className="text-sm text-gray-500">دعم الكتابة من اليمين إلى اليسار</p>
            </div>
            <Switch
              checked={appearanceSettings.rtlSupport}
              onChange={(checked) => setAppearanceSettings(prev => ({ ...prev, rtlSupport: checked }))}
            />
          </div>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>الشعار والأيقونات</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">شعار الموقع</label>
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                <img src={appearanceSettings.logoUrl} alt="Logo" className="max-w-full max-h-full" />
              </div>
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4" />
                <span className="mr-2">تغيير الشعار</span>
              </Button>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">أيقونة الموقع (Favicon)</label>
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center">
                <img src={appearanceSettings.faviconUrl} alt="Favicon" className="max-w-full max-h-full" />
              </div>
              <Button variant="outline" size="sm">
                <Upload className="w-4 h-4" />
                <span className="mr-2">تغيير الأيقونة</span>
              </Button>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  )

  const renderBackupSettings = () => (
    <div className="space-y-6">
      <Card>
        <Card.Header>
          <Card.Title>النسخ الاحتياطي التلقائي</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <Select
            label="تكرار النسخ الاحتياطي"
            value={securitySettings.backupFrequency}
            onChange={(value) => setSecuritySettings(prev => ({ ...prev, backupFrequency: value }))}
            options={backupFrequencies}
          />
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3 space-x-reverse">
              <Database className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900">آخر نسخة احتياطية</h4>
                <p className="text-sm text-blue-700">15 يناير 2025 - 02:30 ص</p>
                <p className="text-xs text-blue-600 mt-1">حجم الملف: 45.2 MB</p>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>

      <Card>
        <Card.Header>
          <Card.Title>إدارة البيانات</Card.Title>
        </Card.Header>
        <Card.Content className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" onClick={handleCreateBackup}>
              <Database className="w-5 h-5" />
              <span className="mr-2">إنشاء نسخة احتياطية</span>
            </Button>
            
            <Button variant="outline" onClick={handleExportData}>
              <Download className="w-5 h-5" />
              <span className="mr-2">تصدير البيانات</span>
            </Button>
            
            <Button variant="outline" onClick={handleImportData}>
              <Upload className="w-5 h-5" />
              <span className="mr-2">استيراد البيانات</span>
            </Button>
            
            <Button variant="outline" className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400">
              <Trash2 className="w-5 h-5" />
              <span className="mr-2">حذف جميع البيانات</span>
            </Button>
          </div>
          
          <Alert variant="warning" title="تحذير">
            تأكد من إنشاء نسخة احتياطية قبل حذف أي بيانات. لا يمكن التراجع عن عملية الحذف.
          </Alert>
        </Card.Content>
      </Card>
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings()
      case 'security':
        return renderSecuritySettings()
      case 'notifications':
        return renderNotificationSettings()
      case 'appearance':
        return renderAppearanceSettings()
      case 'backup':
        return renderBackupSettings()
      default:
        return renderGeneralSettings()
    }
  }

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <PageHeader
        title="إعدادات النظام"
        description="إدارة إعدادات الموقع والنظام"
        action={
          <Button variant="primary" onClick={handleSaveSettings}>
            <Save className="w-5 h-5" />
            <span className="mr-2">حفظ جميع الإعدادات</span>
          </Button>
        }
      />

      {/* تنبيه النجاح */}
      {showSuccessAlert && (
        <Alert variant="success" title="تم الحفظ بنجاح">
          تم حفظ جميع الإعدادات بنجاح
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* قائمة التبويبات */}
        <div className="lg:col-span-1">
          <Card>
            <Card.Content className="p-4">
              <nav className="space-y-2">
                {tabs.map(tab => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 space-x-reverse px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-iraqi-blue text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{tab.label}</span>
                    </button>
                  )
                })}
              </nav>
            </Card.Content>
          </Card>
        </div>

        {/* محتوى التبويب */}
        <div className="lg:col-span-3">
          {renderTabContent()}
        </div>
      </div>
    </div>
  )
}

export default AdminSettings
