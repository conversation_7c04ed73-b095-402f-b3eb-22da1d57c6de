import React, { useState } from 'react'
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  MessageSquare,
  Facebook,
  Twitter,
  Instagram,
  Youtube,
  Globe,
  Calendar,
  Users,
  FileText
} from 'lucide-react'
import { Card, ContactForm, PageHeader } from '../components'

const Contact = () => {

  const contactInfo = [
    {
      icon: Phone,
      title: 'الهاتف',
      details: ['+964-XXX-XXXX-XXX', '+964-XXX-XXXX-XXX'],
      description: 'متاح من الأحد إلى الخميس، 8:00 ص - 4:00 م'
    },
    {
      icon: Mail,
      title: 'البريد الإلكتروني',
      details: ['<EMAIL>', '<EMAIL>'],
      description: 'نرد على جميع الرسائل خلال 24 ساعة'
    },
    {
      icon: MapPin,
      title: 'العنوان',
      details: ['مجلس النواب العراقي', 'المنطقة الخضراء، بغداد، العراق'],
      description: 'مكت<PERSON> رقم XXX، الطابق الثاني'
    },
    {
      icon: Clock,
      title: 'ساعات العمل',
      details: ['الأحد - الخميس: 8:00 ص - 4:00 م', 'الجمعة - السبت: مغلق'],
      description: 'للحالات العاجلة، يرجى الاتصال على الرقم المخصص'
    }
  ]

  const socialMedia = [
    { icon: Facebook, name: 'Facebook', url: '#', color: 'bg-blue-600' },
    { icon: Twitter, name: 'Twitter', url: '#', color: 'bg-sky-500' },
    { icon: Instagram, name: 'Instagram', url: '#', color: 'bg-pink-600' },
    { icon: Youtube, name: 'YouTube', url: '#', color: 'bg-red-600' },
    { icon: Globe, name: 'الموقع الرسمي', url: '#', color: 'bg-gray-600' }
  ]

  const officeServices = [
    {
      icon: Calendar,
      title: 'حجز موعد',
      description: 'احجز موعداً لمقابلة النائب أو فريق العمل'
    },
    {
      icon: Users,
      title: 'استقبال الوفود',
      description: 'استقبال الوفود والمجموعات المجتمعية'
    },
    {
      icon: FileText,
      title: 'تقديم الطلبات',
      description: 'تقديم الطلبات والشكاوى والاقتراحات'
    },
    {
      icon: MessageSquare,
      title: 'الاستشارات',
      description: 'الحصول على استشارات في القضايا المختلفة'
    }
  ]

  const handleFormSubmit = async (formData) => {
    console.log('Contact form submitted:', formData)
    // هنا يمكن إرسال البيانات إلى الخادم
    return new Promise(resolve => setTimeout(resolve, 1000))
  }

  return (
    <div className="min-h-screen">
      {/* رأس الصفحة */}
      <PageHeader
        title="تواصل معنا"
        subtitle="نحن هنا للاستماع إليكم ومساعدتكم. تواصلوا معنا عبر أي من الطرق المتاحة"
        breadcrumbs={[
          { label: 'تواصل معنا' }
        ]}
      />

      <div className="container-custom py-12">

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* نموذج التواصل */}
          <Card>
            <Card.Header>
              <Card.Title>أرسل رسالة</Card.Title>
              <Card.Description>
                املأ النموذج أدناه وسنقوم بالرد عليك في أقرب وقت ممكن
              </Card.Description>
            </Card.Header>

            <Card.Content>
              <ContactForm onSubmit={handleFormSubmit} />
            </Card.Content>
          </Card>

          {/* معلومات التواصل */}
          <div className="space-y-8">
            <Card>
              <Card.Header>
                <Card.Title>معلومات التواصل</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="space-y-6">
                  {contactInfo.map((info, index) => {
                    const Icon = info.icon
                    return (
                      <div key={index} className="flex items-start space-x-4 space-x-reverse">
                        <div className="w-12 h-12 bg-iraqi-blue rounded-lg flex items-center justify-center flex-shrink-0">
                          <Icon className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h4 className="text-lg font-semibold text-gray-900 mb-2">
                            {info.title}
                          </h4>
                          {info.details.map((detail, idx) => (
                            <p key={idx} className="text-gray-700 font-medium">
                              {detail}
                            </p>
                          ))}
                          <p className="text-gray-500 text-sm mt-1">
                            {info.description}
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </Card.Content>
            </Card>

            {/* وسائل التواصل الاجتماعي */}
            <Card>
              <Card.Header>
                <Card.Title>تابعنا على</Card.Title>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-2 gap-4">
                  {socialMedia.map((social, index) => {
                    const Icon = social.icon
                    return (
                      <a
                        key={index}
                        href={social.url}
                        className={`${social.color} text-white p-4 rounded-lg hover:opacity-90 transition-opacity flex items-center space-x-3 space-x-reverse`}
                      >
                        <Icon className="w-5 h-5" />
                        <span className="font-medium">{social.name}</span>
                      </a>
                    )
                  })}
                </div>
              </Card.Content>
            </Card>
          </div>
        </div>

        {/* خدمات المكتب */}
        <div className="mb-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">خدمات المكتب</h2>
            <p className="text-xl text-gray-600">
              الخدمات المتاحة في مكتب النائب
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {officeServices.map((service, index) => {
              const Icon = service.icon
              return (
                <div key={index} className="card text-center hover:shadow-lg transition-shadow">
                  <div className="w-16 h-16 bg-iraqi-blue rounded-full mx-auto mb-4 flex items-center justify-center">
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {service.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>

        {/* الخريطة */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-2xl font-bold text-gray-900">موقع المكتب</h2>
            <p className="text-gray-600">
              مجلس النواب العراقي، المنطقة الخضراء، بغداد
            </p>
          </div>
          
          <div className="aspect-w-16 aspect-h-9">
            <div className="w-full h-96 bg-gray-200 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">خريطة الموقع</p>
                <p className="text-gray-400 text-sm">
                  مجلس النواب العراقي، المنطقة الخضراء، بغداد
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className="mt-12 bg-iraqi-blue text-white rounded-lg p-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">هل تحتاج مساعدة عاجلة؟</h2>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              للحالات العاجلة والطارئة، يمكنكم التواصل معنا مباشرة عبر الهاتف أو زيارة المكتب خلال ساعات العمل الرسمية
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:+964XXXXXXXXX"
                className="bg-white text-iraqi-blue px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center justify-center"
              >
                <Phone className="w-5 h-5 ml-2" />
                اتصل الآن
              </a>
              <a
                href="mailto:<EMAIL>"
                className="bg-iraqi-gold text-white px-6 py-3 rounded-lg font-semibold hover:bg-yellow-600 transition-colors inline-flex items-center justify-center"
              >
                <Mail className="w-5 h-5 ml-2" />
                أرسل إيميل
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Contact
