import React, { useState, useEffect } from 'react'
import {
  Search,
  Filter,
  Calendar,
  DollarSign,
  Users,
  CheckCircle,
  Clock,
  AlertCircle,
  Download,
  Eye,
  MapPin,
  Building,
  Truck,
  Heart,
  Bookmark,
  Share2,
  TrendingUp,
  BarChart3,
  PieChart,
  Target,
  Award,
  Zap,
  Shield,
  Globe,
  Home,
  GraduationCap,
  Hospital,
  Leaf,
  Factory,
  ChevronLeft,
  ChevronRight,
  Grid,
  List,
  SortAsc,
  SortDesc,
  Star,
  Play,
  ExternalLink,
  FileText,
  Image,
  Video,
  Phone,
  Mail,
  MessageSquare,
  ThumbsUp,
  Flag,
  Layers,
  Settings,
  Info,
  Plus,
  Minus,
  X
} from 'lucide-react'

const Projects = () => {
  // إدارة الحالة المتقدمة
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedLocation, setSelectedLocation] = useState('all')
  const [sortBy, setSortBy] = useState('newest')
  const [viewMode, setViewMode] = useState('grid') // grid or list
  const [currentPage, setCurrentPage] = useState(1)
  const [isVisible, setIsVisible] = useState({})
  const [selectedProject, setSelectedProject] = useState(null)
  const [showProjectModal, setShowProjectModal] = useState(false)
  const [bookmarkedProjects, setBookmarkedProjects] = useState([])
  const [likedProjects, setLikedProjects] = useState([])
  const [showMap, setShowMap] = useState(false)
  const projectsPerPage = 6

  // تكوين الفئات مع الأيقونات والألوان
  const categories = [
    { id: 'all', name: 'جميع الفئات', icon: Globe, color: 'bg-gray-500' },
    { id: 'infrastructure', name: 'البنية التحتية', icon: Building, color: 'bg-iraqi-blue' },
    { id: 'education', name: 'التعليم', icon: GraduationCap, color: 'bg-iraqi-green' },
    { id: 'health', name: 'الصحة', icon: Hospital, color: 'bg-red-500' },
    { id: 'housing', name: 'الإسكان', icon: Home, color: 'bg-orange-500' },
    { id: 'environment', name: 'البيئة', icon: Leaf, color: 'bg-green-600' },
    { id: 'economy', name: 'الاقتصاد', icon: Factory, color: 'bg-purple-500' },
    { id: 'social', name: 'الخدمات الاجتماعية', icon: Users, color: 'bg-pink-500' },
    { id: 'transport', name: 'النقل والمواصلات', icon: Truck, color: 'bg-indigo-500' }
  ]

  // حالات المشاريع مع الأيقونات والألوان
  const statuses = [
    { id: 'all', name: 'جميع الحالات', icon: Globe, color: 'bg-gray-500' },
    { id: 'completed', name: 'مكتمل', icon: CheckCircle, color: 'bg-green-500' },
    { id: 'in_progress', name: 'قيد التنفيذ', icon: Clock, color: 'bg-iraqi-blue' },
    { id: 'planned', name: 'مخطط', icon: Calendar, color: 'bg-iraqi-gold' },
    { id: 'suspended', name: 'متوقف', icon: AlertCircle, color: 'bg-red-500' },
    { id: 'under_review', name: 'قيد المراجعة', icon: Eye, color: 'bg-orange-500' }
  ]

  // المواقع الجغرافية
  const locations = [
    { id: 'all', name: 'جميع المحافظات', icon: Globe },
    { id: 'baghdad', name: 'بغداد', icon: MapPin },
    { id: 'basra', name: 'البصرة', icon: MapPin },
    { id: 'mosul', name: 'الموصل', icon: MapPin },
    { id: 'erbil', name: 'أربيل', icon: MapPin },
    { id: 'najaf', name: 'النجف', icon: MapPin },
    { id: 'karbala', name: 'كربلاء', icon: MapPin },
    { id: 'sulaymaniyah', name: 'السليمانية', icon: MapPin },
    { id: 'diyala', name: 'ديالى', icon: MapPin }
  ]

  // خيارات الترتيب
  const sortOptions = [
    { value: 'newest', label: 'الأحدث', icon: SortDesc },
    { value: 'oldest', label: 'الأقدم', icon: SortAsc },
    { value: 'budget_high', label: 'الميزانية (الأعلى)', icon: TrendingUp },
    { value: 'budget_low', label: 'الميزانية (الأقل)', icon: SortAsc },
    { value: 'progress_high', label: 'التقدم (الأعلى)', icon: BarChart3 },
    { value: 'beneficiaries_high', label: 'المستفيدين (الأكثر)', icon: Users },
    { value: 'alphabetical', label: 'أبجدياً', icon: SortAsc }
  ]

  // إعداد Intersection Observer للرسوم المتحركة
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(prev => ({
              ...prev,
              [entry.target.id]: true
            }))
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = document.querySelectorAll('[data-animate]')
    elements.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  const projectsData = [
    {
      id: 1,
      title: 'مشروع تطوير شبكة الطرق السريعة بغداد-البصرة',
      description: 'مشروع استراتيجي لتطوير وتوسيع الطريق السريع الرابط بين العاصمة بغداد ومحافظة البصرة، يشمل إنشاء 4 حارات مرورية في كل اتجاه مع جسور حديثة ومحطات خدمات متطورة.',
      category: 'infrastructure',
      status: 'in_progress',
      progress: 78,
      budget: 850000000,
      startDate: '2023-03-15',
      endDate: '2025-12-30',
      beneficiaries: 2500000,
      contractor: 'شركة الإعمار العراقية للطرق والجسور',
      location: 'baghdad',
      coordinates: { lat: 33.3152, lng: 44.3661 },
      priority: 'high',
      featured: true,
      images: ['/images/project1-1.jpg', '/images/project1-2.jpg'],
      videos: ['/videos/project1.mp4'],
      documents: [
        { name: 'تقرير التقدم الشهري.pdf', type: 'report', size: '2.5 MB' },
        { name: 'المخططات الهندسية.pdf', type: 'technical', size: '15.8 MB' },
        { name: 'دراسة الأثر البيئي.pdf', type: 'environmental', size: '8.2 MB' }
      ],
      milestones: [
        { name: 'بداية المشروع', date: '2023-03-15', completed: true },
        { name: 'إنجاز 50% من الأعمال', date: '2024-06-30', completed: true },
        { name: 'إنجاز الجسور الرئيسية', date: '2025-03-15', completed: false },
        { name: 'الافتتاح الرسمي', date: '2025-12-30', completed: false }
      ],
      team: {
        projectManager: 'م. أحمد علي الخفاجي',
        supervisor: 'د. فاطمة محمد الزهراء',
        contractor: 'شركة الإعمار العراقية للطرق والجسور'
      },
      tags: ['طرق سريعة', 'بنية تحتية', 'نقل', 'تنمية اقتصادية'],
      likes: 1247,
      views: 15680,
      shares: 234
    },
    {
      id: 2,
      title: 'مجمع المدارس النموذجية في الكرخ',
      description: 'إنشاء مجمع تعليمي متطور يضم 12 مدرسة (ابتدائية، متوسطة، إعدادية) مجهزة بأحدث التقنيات التعليمية والمختبرات العلمية ومكتبات رقمية لخدمة 8000 طالب وطالبة.',
      category: 'education',
      status: 'in_progress',
      progress: 65,
      budget: 420000000,
      startDate: '2024-01-10',
      endDate: '2026-09-01',
      beneficiaries: 8000,
      contractor: 'شركة البناء التعليمي المتطور',
      location: 'baghdad',
      coordinates: { lat: 33.3406, lng: 44.4009 },
      priority: 'high',
      featured: true,
      images: ['/images/project2-1.jpg', '/images/project2-2.jpg'],
      videos: ['/videos/project2.mp4'],
      documents: [
        { name: 'التصاميم المعمارية.pdf', type: 'architectural', size: '25.3 MB' },
        { name: 'دراسة الجدوى التعليمية.pdf', type: 'feasibility', size: '5.7 MB' },
        { name: 'تقرير التقدم.pdf', type: 'report', size: '3.2 MB' }
      ],
      milestones: [
        { name: 'وضع حجر الأساس', date: '2024-01-10', completed: true },
        { name: 'إنجاز الهيكل الخرساني', date: '2024-12-15', completed: true },
        { name: 'تركيب التجهيزات التعليمية', date: '2025-06-30', completed: false },
        { name: 'الافتتاح الرسمي', date: '2026-09-01', completed: false }
      ],
      team: {
        projectManager: 'د. سارة أحمد الجبوري',
        supervisor: 'م. محمد حسن العبيدي',
        contractor: 'شركة البناء التعليمي المتطور'
      },
      tags: ['تعليم', 'مدارس', 'تقنيات حديثة', 'مختبرات'],
      likes: 892,
      views: 12450,
      shares: 178
    },
    {
      id: 3,
      title: 'مستشفى الأطفال التخصصي في البصرة',
      description: 'إنشاء مستشفى متخصص لطب الأطفال بسعة 300 سرير، مجهز بأحدث المعدات الطبية ووحدات العناية المركزة للأطفال والخدج، مع مركز للأبحاث الطبية.',
      category: 'health',
      status: 'planned',
      progress: 15,
      budget: 680000000,
      startDate: '2025-02-01',
      endDate: '2027-08-31',
      beneficiaries: 500000,
      contractor: 'شركة المشاريع الطبية الدولية',
      location: 'basra',
      coordinates: { lat: 30.5085, lng: 47.7804 },
      priority: 'high',
      featured: true,
      images: ['/images/project3-1.jpg'],
      videos: [],
      documents: [
        { name: 'دراسة الجدوى الطبية.pdf', type: 'medical', size: '12.4 MB' },
        { name: 'التصاميم الطبية المتخصصة.pdf', type: 'medical', size: '18.7 MB' },
        { name: 'خطة التنفيذ.pdf', type: 'planning', size: '4.1 MB' }
      ],
      milestones: [
        { name: 'الموافقة على التصاميم', date: '2024-12-15', completed: true },
        { name: 'بداية أعمال الحفر', date: '2025-02-01', completed: false },
        { name: 'إنجاز الهيكل الرئيسي', date: '2026-03-15', completed: false },
        { name: 'تركيب المعدات الطبية', date: '2027-05-30', completed: false },
        { name: 'الافتتاح الرسمي', date: '2027-08-31', completed: false }
      ],
      team: {
        projectManager: 'د. علي حسين الناصر',
        supervisor: 'د. زينب محمد الخالدي',
        contractor: 'شركة المشاريع الطبية الدولية'
      },
      tags: ['صحة', 'أطفال', 'مستشفى تخصصي', 'عناية مركزة'],
      likes: 1156,
      views: 18920,
      shares: 312
    },
    {
      id: 4,
      title: 'مشروع الإسكان الاجتماعي في النجف',
      description: 'بناء 2000 وحدة سكنية حديثة للأسر ذات الدخل المحدود، مع توفير جميع الخدمات الأساسية من كهرباء وماء وصرف صحي، بالإضافة إلى مدارس ومراكز صحية ومناطق ترفيهية.',
      category: 'housing',
      status: 'in_progress',
      progress: 42,
      budget: 320000000,
      startDate: '2024-05-20',
      endDate: '2026-12-31',
      beneficiaries: 12000,
      contractor: 'شركة الإسكان العراقية المحدودة',
      location: 'najaf',
      coordinates: { lat: 32.0000, lng: 44.3333 },
      priority: 'medium',
      featured: false,
      images: ['/images/project4-1.jpg', '/images/project4-2.jpg'],
      videos: ['/videos/project4.mp4'],
      documents: [
        { name: 'مخططات الوحدات السكنية.pdf', type: 'architectural', size: '22.1 MB' },
        { name: 'دراسة الخدمات.pdf', type: 'services', size: '7.8 MB' },
        { name: 'تقرير التقدم الربعي.pdf', type: 'report', size: '4.5 MB' }
      ],
      milestones: [
        { name: 'تخصيص الأراضي', date: '2024-03-15', completed: true },
        { name: 'بداية أعمال البناء', date: '2024-05-20', completed: true },
        { name: 'إنجاز 1000 وحدة', date: '2025-08-15', completed: false },
        { name: 'إنجاز جميع الوحدات', date: '2026-10-30', completed: false },
        { name: 'تسليم المشروع', date: '2026-12-31', completed: false }
      ],
      team: {
        projectManager: 'م. حيدر عبد الرضا',
        supervisor: 'م. نور الهدى الموسوي',
        contractor: 'شركة الإسكان العراقية المحدودة'
      },
      tags: ['إسكان', 'اجتماعي', 'وحدات سكنية', 'خدمات'],
      likes: 567,
      views: 9340,
      shares: 89
    },
    {
      id: 5,
      title: 'محطة معالجة المياه الثقيلة في كربلاء',
      description: 'إنشاء محطة متطورة لمعالجة المياه الثقيلة بطاقة 100,000 متر مكعب يومياً، مع تطبيق أحدث التقنيات البيئية لحماية نهر الفرات والمياه الجوفية.',
      category: 'environment',
      status: 'suspended',
      progress: 28,
      budget: 180000000,
      startDate: '2023-08-01',
      endDate: '2025-12-31',
      beneficiaries: 800000,
      contractor: 'الشركة العراقية للبيئة والمياه',
      location: 'karbala',
      coordinates: { lat: 32.6160, lng: 44.0242 },
      priority: 'high',
      featured: false,
      images: ['/images/project5-1.jpg'],
      videos: [],
      documents: [
        { name: 'دراسة الأثر البيئي.pdf', type: 'environmental', size: '14.2 MB' },
        { name: 'تقرير التوقف المؤقت.pdf', type: 'report', size: '3.7 MB' },
        { name: 'خطة إعادة التشغيل.pdf', type: 'planning', size: '6.1 MB' }
      ],
      milestones: [
        { name: 'بداية المشروع', date: '2023-08-01', completed: true },
        { name: 'إنجاز الأعمال المدنية', date: '2024-02-15', completed: true },
        { name: 'توقف مؤقت للمراجعة', date: '2024-06-30', completed: true },
        { name: 'استئناف العمل', date: '2025-01-15', completed: false },
        { name: 'التشغيل التجريبي', date: '2025-10-01', completed: false }
      ],
      team: {
        projectManager: 'د. كريم جواد الكربلائي',
        supervisor: 'م. أمل صادق الحسيني',
        contractor: 'الشركة العراقية للبيئة والمياه'
      },
      tags: ['بيئة', 'معالجة مياه', 'حماية', 'تقنيات حديثة'],
      likes: 423,
      views: 7890,
      shares: 67
    },
    {
      id: 6,
      title: 'مجمع الصناعات الغذائية في الموصل',
      description: 'إنشاء مجمع صناعي متطور للصناعات الغذائية يضم 15 مصنعاً لإنتاج وتعبئة المواد الغذائية، مع مراكز للبحث والتطوير ومختبرات لضمان الجودة.',
      category: 'economy',
      status: 'planned',
      progress: 5,
      budget: 450000000,
      startDate: '2025-06-01',
      endDate: '2027-12-31',
      beneficiaries: 50000,
      contractor: 'شركة التنمية الصناعية العراقية',
      location: 'mosul',
      coordinates: { lat: 36.3489, lng: 43.1189 },
      priority: 'medium',
      featured: false,
      images: ['/images/project6-1.jpg'],
      videos: [],
      documents: [
        { name: 'دراسة الجدوى الاقتصادية.pdf', type: 'economic', size: '9.8 MB' },
        { name: 'التصاميم الصناعية.pdf', type: 'industrial', size: '16.4 MB' },
        { name: 'خطة التوظيف.pdf', type: 'employment', size: '2.9 MB' }
      ],
      milestones: [
        { name: 'الموافقة على المشروع', date: '2024-12-01', completed: false },
        { name: 'بداية أعمال التنفيذ', date: '2025-06-01', completed: false },
        { name: 'إنجاز المرحلة الأولى', date: '2026-06-30', completed: false },
        { name: 'التشغيل التجريبي', date: '2027-09-01', completed: false },
        { name: 'التشغيل الكامل', date: '2027-12-31', completed: false }
      ],
      team: {
        projectManager: 'م. عمر فاضل الموصلي',
        supervisor: 'د. ليلى أحمد النعيمي',
        contractor: 'شركة التنمية الصناعية العراقية'
      },
      tags: ['اقتصاد', 'صناعة', 'غذاء', 'توظيف'],
      likes: 298,
      views: 5670,
      shares: 45
    }
  ]

  // وظائف مساعدة متقدمة
  const getStatusInfo = (statusId) => {
    return statuses.find(s => s.id === statusId) || statuses[0]
  }

  const getCategoryInfo = (categoryId) => {
    return categories.find(c => c.id === categoryId) || categories[0]
  }

  const getLocationInfo = (locationId) => {
    return locations.find(l => l.id === locationId) || locations[0]
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-IQ', {
      style: 'currency',
      currency: 'IQD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-IQ', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const calculateDaysRemaining = (endDate) => {
    const today = new Date()
    const end = new Date(endDate)
    const diffTime = end - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays > 0 ? diffDays : 0
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityText = (priority) => {
    switch (priority) {
      case 'high': return 'عالية'
      case 'medium': return 'متوسطة'
      case 'low': return 'منخفضة'
      default: return 'غير محدد'
    }
  }

  // وظائف التفاعل
  const handleLike = (projectId) => {
    setLikedProjects(prev =>
      prev.includes(projectId)
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    )
  }

  const handleBookmark = (projectId) => {
    setBookmarkedProjects(prev =>
      prev.includes(projectId)
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    )
  }

  const handleShare = (project) => {
    if (navigator.share) {
      navigator.share({
        title: project.title,
        text: project.description,
        url: window.location.href
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      alert('تم نسخ الرابط إلى الحافظة')
    }
  }

  const openProjectModal = (project) => {
    setSelectedProject(project)
    setShowProjectModal(true)
  }

  // تأثيرات الرسوم المتحركة
  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setIsVisible(prev => ({
            ...prev,
            [entry.target.id]: true
          }))
        }
      })
    }, observerOptions)

    // مراقبة جميع العناصر القابلة للرسوم المتحركة
    const animatedElements = document.querySelectorAll('[data-animate]')
    animatedElements.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  // إضافة فئات CSS للرسوم المتحركة
  useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      [data-animate] {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.6s ease-out;
      }
      [data-animate].animate-in {
        opacity: 1;
        transform: translateY(0);
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)
    return () => document.head.removeChild(style)
  }, [])

  // تطبيق الرسوم المتحركة
  useEffect(() => {
    Object.keys(isVisible).forEach(id => {
      if (isVisible[id]) {
        const element = document.getElementById(id)
        if (element) {
          element.classList.add('animate-in')
        }
      }
    })
  }, [isVisible])

  // منطق التصفية والترتيب المتقدم
  const getFilteredAndSortedProjects = () => {
    let filtered = projectsData.filter(project => {
      const matchesSearch =
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())) ||
        project.contractor.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = selectedStatus === 'all' || project.status === selectedStatus
      const matchesCategory = selectedCategory === 'all' || project.category === selectedCategory
      const matchesLocation = selectedLocation === 'all' || project.location === selectedLocation

      return matchesSearch && matchesStatus && matchesCategory && matchesLocation
    })

    // ترتيب النتائج
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.startDate) - new Date(a.startDate)
        case 'oldest':
          return new Date(a.startDate) - new Date(b.startDate)
        case 'budget_high':
          return b.budget - a.budget
        case 'budget_low':
          return a.budget - b.budget
        case 'progress_high':
          return b.progress - a.progress
        case 'beneficiaries_high':
          return b.beneficiaries - a.beneficiaries
        case 'alphabetical':
          return a.title.localeCompare(b.title, 'ar')
        default:
          return 0
      }
    })

    return filtered
  }

  const allProjects = getFilteredAndSortedProjects()
  const featuredProjects = allProjects.filter(project => project.featured)

  // منطق الصفحات
  const totalPages = Math.ceil(allProjects.length / projectsPerPage)
  const startIndex = (currentPage - 1) * projectsPerPage
  const paginatedProjects = allProjects.slice(startIndex, startIndex + projectsPerPage)

  // إحصائيات متقدمة
  const totalBudget = projectsData.reduce((sum, project) => sum + project.budget, 0)
  const completedProjects = projectsData.filter(p => p.status === 'completed').length
  const activeProjects = projectsData.filter(p => p.status === 'in_progress').length
  const totalBeneficiaries = projectsData.reduce((sum, project) => sum + project.beneficiaries, 0)
  const averageProgress = Math.round(
    projectsData.reduce((sum, project) => sum + project.progress, 0) / projectsData.length
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-iraqi-blue via-blue-800 to-iraqi-blue text-white py-20 overflow-hidden">
        {/* خلفية زخرفية */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 right-10 w-32 h-32 border-2 border-white rounded-full"></div>
          <div className="absolute bottom-10 left-10 w-24 h-24 border-2 border-iraqi-gold rounded-full"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 border border-white transform rotate-45"></div>
        </div>

        <div className="container-custom relative z-10">
          <div className="text-center mb-12" data-animate id="hero-content">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
              المشاريع والإنجازات
            </h1>
            <p className="text-xl md:text-2xl text-blue-100 max-w-4xl mx-auto leading-relaxed">
              نعمل على تنفيذ مشاريع تنموية شاملة لخدمة المواطنين وتطوير البنية التحتية في جميع أنحاء العراق
            </p>
          </div>

          {/* إحصائيات متقدمة */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-6 max-w-6xl mx-auto">
            <div className="text-center" data-animate id="stat-1">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Building className="w-8 h-8 text-iraqi-gold" />
              </div>
              <div className="text-3xl font-bold text-iraqi-gold">{projectsData.length}</div>
              <div className="text-blue-100">إجمالي المشاريع</div>
            </div>

            <div className="text-center" data-animate id="stat-2">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
              <div className="text-3xl font-bold text-green-400">{completedProjects}</div>
              <div className="text-blue-100">مشاريع مكتملة</div>
            </div>

            <div className="text-center" data-animate id="stat-3">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Clock className="w-8 h-8 text-yellow-400" />
              </div>
              <div className="text-3xl font-bold text-yellow-400">{activeProjects}</div>
              <div className="text-blue-100">مشاريع نشطة</div>
            </div>

            <div className="text-center" data-animate id="stat-4">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Users className="w-8 h-8 text-iraqi-green" />
              </div>
              <div className="text-3xl font-bold text-iraqi-green">{(totalBeneficiaries / 1000000).toFixed(1)}M</div>
              <div className="text-blue-100">المستفيدون</div>
            </div>

            <div className="text-center" data-animate id="stat-5">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <TrendingUp className="w-8 h-8 text-iraqi-red" />
              </div>
              <div className="text-3xl font-bold text-iraqi-red">{averageProgress}%</div>
              <div className="text-blue-100">متوسط التقدم</div>
            </div>
          </div>
        </div>
      </section>

      <div className="container-custom py-12">
        {/* المشاريع المميزة */}
        {featuredProjects.length > 0 && (
          <section className="mb-16" data-animate id="featured-projects">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">المشاريع المميزة</h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                أهم المشاريع الاستراتيجية التي تساهم في التنمية الشاملة للعراق
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredProjects.slice(0, 4).map((project, index) => {
                const statusInfo = getStatusInfo(project.status)
                const categoryInfo = getCategoryInfo(project.category)
                const StatusIcon = statusInfo.icon
                const CategoryIcon = categoryInfo.icon

                return (
                  <div
                    key={project.id}
                    className="group relative bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    onClick={() => openProjectModal(project)}
                  >
                    {/* صورة المشروع */}
                    <div className="relative h-64 bg-gradient-to-br from-gray-200 to-gray-300 overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <CategoryIcon className="w-20 h-20 text-white opacity-50" />
                      </div>

                      {/* شارات الحالة والأولوية */}
                      <div className="absolute top-4 right-4 flex gap-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium text-white ${statusInfo.color}`}>
                          {statusInfo.name}
                        </span>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getPriorityColor(project.priority)}`}>
                          {getPriorityText(project.priority)}
                        </span>
                      </div>

                      {/* إحصائيات سريعة */}
                      <div className="absolute bottom-4 left-4 right-4 flex justify-between text-white text-sm">
                        <div className="flex items-center">
                          <Eye className="w-4 h-4 ml-1" />
                          {project.views.toLocaleString()}
                        </div>
                        <div className="flex items-center">
                          <Heart className="w-4 h-4 ml-1" />
                          {project.likes}
                        </div>
                      </div>
                    </div>

                    {/* محتوى البطاقة */}
                    <div className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <h3 className="text-xl font-bold text-gray-900 group-hover:text-iraqi-blue transition-colors duration-300 line-clamp-2">
                          {project.title}
                        </h3>
                        <div className="flex items-center gap-2 mr-4">
                          <StatusIcon className="w-5 h-5" style={{ color: statusInfo.color.replace('bg-', '').replace('-500', '') }} />
                        </div>
                      </div>

                      <p className="text-gray-600 mb-4 line-clamp-3 leading-relaxed">
                        {project.description}
                      </p>

                      {/* شريط التقدم */}
                      <div className="mb-4">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium text-gray-700">التقدم</span>
                          <span className="text-sm font-bold text-iraqi-blue">{project.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-iraqi-blue to-blue-600 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${project.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* معلومات إضافية */}
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center">
                          <DollarSign className="w-4 h-4 ml-1 text-iraqi-gold" />
                          <span className="font-medium">{formatCurrency(project.budget)}</span>
                        </div>
                        <div className="flex items-center">
                          <Users className="w-4 h-4 ml-1 text-iraqi-green" />
                          <span className="font-medium">{project.beneficiaries.toLocaleString()} مستفيد</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 ml-1 text-iraqi-red" />
                          <span className="font-medium">{getLocationInfo(project.location).name}</span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 ml-1 text-gray-500" />
                          <span className="font-medium">{formatDate(project.endDate)}</span>
                        </div>
                      </div>

                      {/* أزرار التفاعل */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div className="flex items-center gap-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleLike(project.id)
                            }}
                            className={`flex items-center px-3 py-1 rounded-lg transition-all duration-300 ${
                              likedProjects.includes(project.id)
                                ? 'bg-red-100 text-red-600'
                                : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                            }`}
                          >
                            <Heart className="w-4 h-4 ml-1" />
                            إعجاب
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleBookmark(project.id)
                            }}
                            className={`flex items-center px-3 py-1 rounded-lg transition-all duration-300 ${
                              bookmarkedProjects.includes(project.id)
                                ? 'bg-iraqi-blue text-white'
                                : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                            }`}
                          >
                            <Bookmark className="w-4 h-4 ml-1" />
                            حفظ
                          </button>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleShare(project)
                          }}
                          className="flex items-center px-3 py-1 rounded-lg bg-gray-100 text-gray-600 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                        >
                          <Share2 className="w-4 h-4 ml-1" />
                          مشاركة
                        </button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </section>
        )}

        {/* أدوات البحث والتصفية المتقدمة */}
        <section className="mb-12" data-animate id="search-filters">
          <div className="bg-white rounded-xl shadow-lg p-6">
            {/* شريط البحث الرئيسي */}
            <div className="relative mb-6">
              <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="ابحث في المشاريع، الوصف، العلامات، أو المقاول..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-12 pl-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent text-right text-lg"
              />
            </div>

            {/* فلاتر الفئات */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">الفئات</h3>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => {
                  const CategoryIcon = category.icon
                  const isSelected = selectedCategory === category.id
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`flex items-center px-4 py-2 rounded-lg border transition-all duration-300 ${
                        isSelected
                          ? `${category.color} text-white border-transparent shadow-lg`
                          : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                      }`}
                    >
                      <CategoryIcon className="w-4 h-4 ml-2" />
                      {category.name}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* فلاتر الحالة */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">حالة المشروع</h3>
              <div className="flex flex-wrap gap-2">
                {statuses.map((status) => {
                  const StatusIcon = status.icon
                  const isSelected = selectedStatus === status.id
                  return (
                    <button
                      key={status.id}
                      onClick={() => setSelectedStatus(status.id)}
                      className={`flex items-center px-4 py-2 rounded-lg border transition-all duration-300 ${
                        isSelected
                          ? `${status.color} text-white border-transparent shadow-lg`
                          : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                      }`}
                    >
                      <StatusIcon className="w-4 h-4 ml-2" />
                      {status.name}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* فلاتر المواقع */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">المحافظة</h3>
              <div className="flex flex-wrap gap-2">
                {locations.map((location) => {
                  const LocationIcon = location.icon
                  const isSelected = selectedLocation === location.id
                  return (
                    <button
                      key={location.id}
                      onClick={() => setSelectedLocation(location.id)}
                      className={`flex items-center px-4 py-2 rounded-lg border transition-all duration-300 ${
                        isSelected
                          ? 'bg-iraqi-blue text-white border-transparent shadow-lg'
                          : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                      }`}
                    >
                      <LocationIcon className="w-4 h-4 ml-2" />
                      {location.name}
                    </button>
                  )
                })}
              </div>
            </div>

            {/* أدوات الترتيب والعرض */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 pt-6 border-t border-gray-200">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-700">ترتيب حسب:</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent text-sm"
                  >
                    {sortOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <button
                  onClick={() => setShowMap(!showMap)}
                  className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                    showMap
                      ? 'bg-iraqi-green text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-iraqi-green hover:text-white'
                  }`}
                >
                  <MapPin className="w-4 h-4 ml-2" />
                  عرض الخريطة
                </button>
              </div>

              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">
                    {allProjects.length} من {projectsData.length} مشروع
                  </span>
                </div>

                <div className="flex items-center gap-1 border border-gray-300 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded transition-all duration-300 ${
                      viewMode === 'grid'
                        ? 'bg-iraqi-blue text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded transition-all duration-300 ${
                      viewMode === 'list'
                        ? 'bg-iraqi-blue text-white'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* عرض المشاريع */}
        <section data-animate id="projects-list">
          {viewMode === 'grid' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {paginatedProjects.map((project, index) => {
                const statusInfo = getStatusInfo(project.status)
                const categoryInfo = getCategoryInfo(project.category)
                const StatusIcon = statusInfo.icon
                const CategoryIcon = categoryInfo.icon

                return (
                  <div
                    key={project.id}
                    className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    onClick={() => openProjectModal(project)}
                  >
                    {/* صورة المشروع */}
                    <div className="relative h-48 bg-gradient-to-br from-gray-200 to-gray-300 overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <CategoryIcon className="w-16 h-16 text-white opacity-50" />
                      </div>

                      {/* شارات */}
                      <div className="absolute top-3 right-3 flex flex-col gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${statusInfo.color}`}>
                          {statusInfo.name}
                        </span>
                        {project.priority === 'high' && (
                          <span className="px-2 py-1 rounded-full text-xs font-medium bg-red-500 text-white">
                            أولوية عالية
                          </span>
                        )}
                      </div>

                      {/* التقدم */}
                      <div className="absolute bottom-3 left-3 right-3">
                        <div className="flex justify-between items-center mb-1">
                          <span className="text-white text-xs">التقدم</span>
                          <span className="text-white text-xs font-bold">{project.progress}%</span>
                        </div>
                        <div className="w-full bg-white/30 rounded-full h-1">
                          <div
                            className="bg-white h-1 rounded-full transition-all duration-500"
                            style={{ width: `${project.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    {/* محتوى البطاقة */}
                    <div className="p-6">
                      <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-iraqi-blue transition-colors duration-300 line-clamp-2">
                        {project.title}
                      </h3>

                      <p className="text-gray-600 mb-4 line-clamp-3 text-sm leading-relaxed">
                        {project.description}
                      </p>

                      {/* معلومات سريعة */}
                      <div className="grid grid-cols-2 gap-3 text-xs text-gray-600 mb-4">
                        <div className="flex items-center">
                          <DollarSign className="w-3 h-3 ml-1 text-iraqi-gold" />
                          <span>{formatCurrency(project.budget)}</span>
                        </div>
                        <div className="flex items-center">
                          <Users className="w-3 h-3 ml-1 text-iraqi-green" />
                          <span>{project.beneficiaries.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-3 h-3 ml-1 text-iraqi-red" />
                          <span>{getLocationInfo(project.location).name}</span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 ml-1 text-gray-500" />
                          <span>{formatDate(project.endDate)}</span>
                        </div>
                      </div>

                      {/* أزرار التفاعل */}
                      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleLike(project.id)
                            }}
                            className={`p-2 rounded-lg transition-all duration-300 ${
                              likedProjects.includes(project.id)
                                ? 'bg-red-100 text-red-600'
                                : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                            }`}
                          >
                            <Heart className="w-4 h-4" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleBookmark(project.id)
                            }}
                            className={`p-2 rounded-lg transition-all duration-300 ${
                              bookmarkedProjects.includes(project.id)
                                ? 'bg-iraqi-blue text-white'
                                : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                            }`}
                          >
                            <Bookmark className="w-4 h-4" />
                          </button>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleShare(project)
                          }}
                          className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                        >
                          <Share2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}

          {viewMode === 'list' && (
            <div className="space-y-6">
              {paginatedProjects.map((project, index) => {
                const statusInfo = getStatusInfo(project.status)
                const categoryInfo = getCategoryInfo(project.category)
                const StatusIcon = statusInfo.icon
                const CategoryIcon = categoryInfo.icon

                return (
                  <div
                    key={project.id}
                    className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 cursor-pointer"
                    onClick={() => openProjectModal(project)}
                  >
                    <div className="flex flex-col lg:flex-row">
                      {/* صورة المشروع */}
                      <div className="relative lg:w-80 h-48 lg:h-auto bg-gradient-to-br from-gray-200 to-gray-300 overflow-hidden">
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <CategoryIcon className="w-16 h-16 text-white opacity-50" />
                        </div>

                        {/* شارات */}
                        <div className="absolute top-3 right-3 flex flex-col gap-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${statusInfo.color}`}>
                            {statusInfo.name}
                          </span>
                        </div>
                      </div>

                      {/* محتوى المشروع */}
                      <div className="flex-1 p-6">
                        <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-4">
                          <div className="flex-1">
                            <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-iraqi-blue transition-colors duration-300">
                              {project.title}
                            </h3>
                            <p className="text-gray-600 mb-4 line-clamp-2 leading-relaxed">
                              {project.description}
                            </p>
                          </div>

                          <div className="lg:mr-6 lg:text-left">
                            <div className="flex items-center gap-2 mb-2">
                              <StatusIcon className="w-5 h-5" style={{ color: statusInfo.color.replace('bg-', '').replace('-500', '') }} />
                              <span className="text-sm font-medium">{statusInfo.name}</span>
                            </div>
                            <div className="text-sm text-gray-600">
                              {getPriorityText(project.priority)} الأولوية
                            </div>
                          </div>
                        </div>

                        {/* شريط التقدم */}
                        <div className="mb-4">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium text-gray-700">التقدم</span>
                            <span className="text-sm font-bold text-iraqi-blue">{project.progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-iraqi-blue to-blue-600 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${project.progress}%` }}
                            ></div>
                          </div>
                        </div>

                        {/* معلومات تفصيلية */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-4">
                          <div>
                            <div className="flex items-center mb-1">
                              <DollarSign className="w-4 h-4 ml-1 text-iraqi-gold" />
                              <span className="font-medium">الميزانية</span>
                            </div>
                            <div className="font-bold text-gray-900">{formatCurrency(project.budget)}</div>
                          </div>
                          <div>
                            <div className="flex items-center mb-1">
                              <Users className="w-4 h-4 ml-1 text-iraqi-green" />
                              <span className="font-medium">المستفيدون</span>
                            </div>
                            <div className="font-bold text-gray-900">{project.beneficiaries.toLocaleString()}</div>
                          </div>
                          <div>
                            <div className="flex items-center mb-1">
                              <MapPin className="w-4 h-4 ml-1 text-iraqi-red" />
                              <span className="font-medium">الموقع</span>
                            </div>
                            <div className="font-bold text-gray-900">{getLocationInfo(project.location).name}</div>
                          </div>
                          <div>
                            <div className="flex items-center mb-1">
                              <Calendar className="w-4 h-4 ml-1 text-gray-500" />
                              <span className="font-medium">الانتهاء</span>
                            </div>
                            <div className="font-bold text-gray-900">{formatDate(project.endDate)}</div>
                          </div>
                        </div>

                        {/* أزرار التفاعل */}
                        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                          <div className="flex items-center gap-3">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleLike(project.id)
                              }}
                              className={`flex items-center px-3 py-2 rounded-lg transition-all duration-300 ${
                                likedProjects.includes(project.id)
                                  ? 'bg-red-100 text-red-600'
                                  : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                              }`}
                            >
                              <Heart className="w-4 h-4 ml-1" />
                              إعجاب ({project.likes})
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleBookmark(project.id)
                              }}
                              className={`flex items-center px-3 py-2 rounded-lg transition-all duration-300 ${
                                bookmarkedProjects.includes(project.id)
                                  ? 'bg-iraqi-blue text-white'
                                  : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                              }`}
                            >
                              <Bookmark className="w-4 h-4 ml-1" />
                              حفظ
                            </button>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="flex items-center text-gray-500 text-sm">
                              <Eye className="w-4 h-4 ml-1" />
                              {project.views.toLocaleString()}
                            </div>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleShare(project)
                              }}
                              className="flex items-center px-3 py-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                            >
                              <Share2 className="w-4 h-4 ml-1" />
                              مشاركة
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </section>

        {/* نظام الصفحات */}
        {totalPages > 1 && (
          <section className="mt-12" data-animate id="pagination">
            <div className="flex items-center justify-center">
              <div className="flex items-center gap-2">
                {/* الصفحة السابقة */}
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="flex items-center px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                >
                  <ChevronRight className="w-4 h-4 ml-1" />
                  السابق
                </button>

                {/* أرقام الصفحات */}
                <div className="flex items-center gap-1">
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                    if (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 2 && page <= currentPage + 2)
                    ) {
                      return (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`w-10 h-10 rounded-lg font-medium transition-all duration-300 ${
                            currentPage === page
                              ? 'bg-iraqi-blue text-white shadow-lg'
                              : 'text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {page}
                        </button>
                      )
                    } else if (
                      page === currentPage - 3 ||
                      page === currentPage + 3
                    ) {
                      return (
                        <span key={page} className="px-2 text-gray-400">
                          ...
                        </span>
                      )
                    }
                    return null
                  })}
                </div>

                {/* الصفحة التالية */}
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="flex items-center px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                >
                  التالي
                  <ChevronLeft className="w-4 h-4 mr-1" />
                </button>
              </div>
            </div>
          </section>
        )}

        {/* رسالة عدم وجود نتائج */}
        {allProjects.length === 0 && (
          <div className="text-center py-16" data-animate id="no-results">
            <div className="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-6 flex items-center justify-center">
              <Search className="w-16 h-16 text-gray-400" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-4">لا توجد مشاريع</h3>
            <p className="text-gray-600 max-w-md mx-auto">
              لم يتم العثور على مشاريع تطابق معايير البحث المحددة. جرب تعديل الفلاتر أو البحث بكلمات مختلفة.
            </p>
            <button
              onClick={() => {
                setSearchTerm('')
                setSelectedCategory('all')
                setSelectedStatus('all')
                setSelectedLocation('all')
                setCurrentPage(1)
              }}
              className="mt-6 px-6 py-3 bg-iraqi-blue text-white rounded-lg hover:bg-blue-700 transition-colors duration-300"
            >
              إعادة تعيين الفلاتر
            </button>
          </div>
        )}

        {/* مودال تفاصيل المشروع */}
        {showProjectModal && selectedProject && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              {/* رأس المودال */}
              <div className="sticky top-0 bg-white border-b border-gray-200 p-6 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {React.createElement(getCategoryInfo(selectedProject.category).icon, {
                      className: "w-6 h-6 text-iraqi-blue"
                    })}
                    <span className={`px-3 py-1 rounded-full text-sm font-medium text-white ${getCategoryInfo(selectedProject.category).color}`}>
                      {getCategoryInfo(selectedProject.category).name}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {React.createElement(getStatusInfo(selectedProject.status).icon, {
                      className: "w-5 h-5"
                    })}
                    <span className={`px-3 py-1 rounded-full text-sm font-medium text-white ${getStatusInfo(selectedProject.status).color}`}>
                      {getStatusInfo(selectedProject.status).name}
                    </span>
                  </div>
                  {selectedProject.priority === 'high' && (
                    <span className="px-3 py-1 rounded-full text-sm font-medium bg-red-500 text-white">
                      أولوية عالية
                    </span>
                  )}
                </div>
                <button
                  onClick={() => setShowProjectModal(false)}
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-300"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              {/* محتوى المودال */}
              <div className="p-6">
                {/* عنوان المشروع */}
                <h1 className="text-4xl font-bold text-gray-900 mb-6 leading-tight">
                  {selectedProject.title}
                </h1>

                {/* معلومات أساسية */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  <div className="bg-gradient-to-br from-iraqi-blue to-blue-600 text-white p-6 rounded-xl">
                    <div className="flex items-center justify-between mb-2">
                      <DollarSign className="w-8 h-8" />
                      <span className="text-blue-100 text-sm">الميزانية</span>
                    </div>
                    <div className="text-2xl font-bold">{formatCurrency(selectedProject.budget)}</div>
                  </div>

                  <div className="bg-gradient-to-br from-iraqi-green to-green-600 text-white p-6 rounded-xl">
                    <div className="flex items-center justify-between mb-2">
                      <Users className="w-8 h-8" />
                      <span className="text-green-100 text-sm">المستفيدون</span>
                    </div>
                    <div className="text-2xl font-bold">{selectedProject.beneficiaries.toLocaleString()}</div>
                  </div>

                  <div className="bg-gradient-to-br from-iraqi-gold to-yellow-600 text-white p-6 rounded-xl">
                    <div className="flex items-center justify-between mb-2">
                      <BarChart3 className="w-8 h-8" />
                      <span className="text-yellow-100 text-sm">التقدم</span>
                    </div>
                    <div className="text-2xl font-bold">{selectedProject.progress}%</div>
                  </div>

                  <div className="bg-gradient-to-br from-iraqi-red to-red-600 text-white p-6 rounded-xl">
                    <div className="flex items-center justify-between mb-2">
                      <Calendar className="w-8 h-8" />
                      <span className="text-red-100 text-sm">الأيام المتبقية</span>
                    </div>
                    <div className="text-2xl font-bold">{calculateDaysRemaining(selectedProject.endDate)}</div>
                  </div>
                </div>

                {/* شريط التقدم التفصيلي */}
                <div className="mb-8">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-xl font-semibold text-gray-900">تقدم المشروع</h3>
                    <span className="text-2xl font-bold text-iraqi-blue">{selectedProject.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-4 mb-4">
                    <div
                      className="bg-gradient-to-r from-iraqi-blue to-blue-600 h-4 rounded-full transition-all duration-1000 relative"
                      style={{ width: `${selectedProject.progress}%` }}
                    >
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  </div>

                  {/* المعالم الزمنية */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-gray-900 mb-3">المعالم الزمنية</h4>
                    {selectedProject.milestones.map((milestone, index) => (
                      <div key={index} className={`flex items-center gap-3 p-3 rounded-lg ${
                        milestone.completed ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border border-gray-200'
                      }`}>
                        {milestone.completed ? (
                          <CheckCircle className="w-5 h-5 text-green-500" />
                        ) : (
                          <Clock className="w-5 h-5 text-gray-400" />
                        )}
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{milestone.name}</div>
                          <div className="text-sm text-gray-600">{formatDate(milestone.date)}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* وصف المشروع */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">وصف المشروع</h3>
                  <div className="prose prose-lg max-w-none">
                    <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                      {selectedProject.description}
                    </p>
                  </div>
                </div>

                {/* معلومات الفريق */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">فريق المشروع</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="font-medium text-gray-900">مدير المشروع</div>
                      <div className="text-gray-600">{selectedProject.team.projectManager}</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="font-medium text-gray-900">المشرف</div>
                      <div className="text-gray-600">{selectedProject.team.supervisor}</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="font-medium text-gray-900">المقاول</div>
                      <div className="text-gray-600">{selectedProject.team.contractor}</div>
                    </div>
                  </div>
                </div>

                {/* الوثائق */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">الوثائق والملفات</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {selectedProject.documents.map((doc, index) => (
                      <div key={index} className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-300 cursor-pointer">
                        <FileText className="w-8 h-8 text-iraqi-blue" />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{doc.name}</div>
                          <div className="text-sm text-gray-600">{doc.type} • {doc.size}</div>
                        </div>
                        <Download className="w-5 h-5 text-gray-400" />
                      </div>
                    ))}
                  </div>
                </div>

                {/* العلامات */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">العلامات</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedProject.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-iraqi-blue hover:text-white transition-colors duration-300 cursor-pointer"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* إحصائيات التفاعل */}
                <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                  <div className="flex items-center gap-6 text-gray-600">
                    <div className="flex items-center">
                      <Eye className="w-5 h-5 ml-2" />
                      <span className="font-medium">{selectedProject.views.toLocaleString()}</span>
                      <span className="mr-1">مشاهدة</span>
                    </div>
                    <div className="flex items-center">
                      <Heart className="w-5 h-5 ml-2" />
                      <span className="font-medium">{selectedProject.likes}</span>
                      <span className="mr-1">إعجاب</span>
                    </div>
                    <div className="flex items-center">
                      <Share2 className="w-5 h-5 ml-2" />
                      <span className="font-medium">{selectedProject.shares}</span>
                      <span className="mr-1">مشاركة</span>
                    </div>
                  </div>

                  {/* أزرار التفاعل */}
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => handleLike(selectedProject.id)}
                      className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                        likedProjects.includes(selectedProject.id)
                          ? 'bg-red-100 text-red-600'
                          : 'bg-gray-100 text-gray-600 hover:bg-red-100 hover:text-red-600'
                      }`}
                    >
                      <Heart className="w-4 h-4 ml-1" />
                      إعجاب
                    </button>
                    <button
                      onClick={() => handleBookmark(selectedProject.id)}
                      className={`flex items-center px-4 py-2 rounded-lg transition-all duration-300 ${
                        bookmarkedProjects.includes(selectedProject.id)
                          ? 'bg-iraqi-blue text-white'
                          : 'bg-gray-100 text-gray-600 hover:bg-iraqi-blue hover:text-white'
                      }`}
                    >
                      <Bookmark className="w-4 h-4 ml-1" />
                      حفظ
                    </button>
                    <button
                      onClick={() => handleShare(selectedProject)}
                      className="flex items-center px-4 py-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-iraqi-green hover:text-white transition-all duration-300"
                    >
                      <Share2 className="w-4 h-4 ml-1" />
                      مشاركة
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

      </div>
    </div>
  )
}

export default Projects
