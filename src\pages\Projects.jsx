import React, { useState } from 'react'
import { 
  Search, 
  Filter, 
  Calendar, 
  DollarSign, 
  Users,
  CheckCircle,
  Clock,
  AlertCircle,
  Download,
  Eye
} from 'lucide-react'

const Projects = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('الكل')
  const [selectedCategory, setSelectedCategory] = useState('الكل')

  const statuses = ['الكل', 'مكتمل', 'قيد التنفيذ', 'متوقف', 'مخطط']
  const categories = ['الكل', 'بنية تحتية', 'تعليم', 'صحة', 'اجتماعي', 'اقتصادي', 'بيئي']

  const projectsData = [
    {
      id: 1,
      title: 'مشروع تطوير البنية التحتية للطرق',
      description: 'مشروع شامل لتطوير وصيانة الطرق الرئيسية في المحافظة لتحسين النقل والمواصلات',
      category: 'بنية تحتية',
      status: 'قيد التنفيذ',
      progress: 75,
      budget: 50000000,
      startDate: '2024-01-15',
      endDate: '2025-06-30',
      beneficiaries: 100000,
      contractor: 'شركة الإعمار العراقية',
      documents: ['تقرير التقدم.pdf', 'المخططات الهندسية.pdf']
    },
    {
      id: 2,
      title: 'برنامج دعم الأسر المحتاجة',
      description: 'برنامج اجتماعي شامل لدعم الأسر ذات الدخل المحدود وتوفير المساعدات الأساسية',
      category: 'اجتماعي',
      status: 'مكتمل',
      progress: 100,
      budget: 25000000,
      startDate: '2023-03-01',
      endDate: '2024-12-31',
      beneficiaries: 5000,
      contractor: 'وزارة العمل والشؤون الاجتماعية',
      documents: ['التقرير النهائي.pdf', 'قائمة المستفيدين.pdf']
    },
    {
      id: 3,
      title: 'مشروع بناء المدارس الحديثة',
      description: 'إنشاء مدارس حديثة مجهزة بأحدث التقنيات التعليمية لتطوير التعليم في المنطقة',
      category: 'تعليم',
      status: 'قيد التنفيذ',
      progress: 45,
      budget: 35000000,
      startDate: '2024-09-01',
      endDate: '2026-08-31',
      beneficiaries: 15000,
      contractor: 'شركة البناء المتطور',
      documents: ['دراسة الجدوى.pdf', 'التصاميم المعمارية.pdf']
    },
    {
      id: 4,
      title: 'مشروع تطوير المراكز الصحية',
      description: 'تطوير وتحديث المراكز الصحية وتزويدها بالمعدات الطبية الحديثة',
      category: 'صحة',
      status: 'مخطط',
      progress: 0,
      budget: 40000000,
      startDate: '2025-04-01',
      endDate: '2026-12-31',
      beneficiaries: 80000,
      contractor: 'وزارة الصحة',
      documents: ['الخطة التنفيذية.pdf']
    },
    {
      id: 5,
      title: 'مشروع معالجة المياه الثقيلة',
      description: 'إنشاء محطات معالجة المياه الثقيلة لحماية البيئة وتحسين جودة المياه',
      category: 'بيئي',
      status: 'متوقف',
      progress: 30,
      budget: 60000000,
      startDate: '2023-06-01',
      endDate: '2025-12-31',
      beneficiaries: 200000,
      contractor: 'الشركة العراقية للبيئة',
      documents: ['تقرير التوقف.pdf', 'دراسة الأثر البيئي.pdf']
    }
  ]

  const getStatusIcon = (status) => {
    switch (status) {
      case 'مكتمل':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'قيد التنفيذ':
        return <Clock className="w-5 h-5 text-blue-500" />
      case 'متوقف':
        return <AlertCircle className="w-5 h-5 text-red-500" />
      case 'مخطط':
        return <Calendar className="w-5 h-5 text-gray-500" />
      default:
        return null
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'مكتمل':
        return 'bg-green-100 text-green-800'
      case 'قيد التنفيذ':
        return 'bg-blue-100 text-blue-800'
      case 'متوقف':
        return 'bg-red-100 text-red-800'
      case 'مخطط':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredProjects = projectsData.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'الكل' || project.status === selectedStatus
    const matchesCategory = selectedCategory === 'الكل' || project.category === selectedCategory
    return matchesSearch && matchesStatus && matchesCategory
  })

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-IQ', {
      style: 'currency',
      currency: 'IQD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const totalBudget = projectsData.reduce((sum, project) => sum + project.budget, 0)
  const completedProjects = projectsData.filter(p => p.status === 'مكتمل').length
  const activeProjects = projectsData.filter(p => p.status === 'قيد التنفيذ').length
  const totalBeneficiaries = projectsData.reduce((sum, project) => sum + project.beneficiaries, 0)

  return (
    <div className="min-h-screen py-12">
      <div className="container-custom">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">المشاريع والإنجازات</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            تعرف على المشاريع التي تم تنفيذها والجاري العمل عليها لخدمة المواطنين
          </p>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="card text-center">
            <div className="w-12 h-12 bg-iraqi-blue rounded-full mx-auto mb-4 flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{completedProjects}</h3>
            <p className="text-gray-600">مشاريع مكتملة</p>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-iraqi-gold rounded-full mx-auto mb-4 flex items-center justify-center">
              <Clock className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{activeProjects}</h3>
            <p className="text-gray-600">مشاريع نشطة</p>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-iraqi-green rounded-full mx-auto mb-4 flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{totalBudget.toLocaleString()}</h3>
            <p className="text-gray-600">إجمالي الميزانية</p>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-iraqi-red rounded-full mx-auto mb-4 flex items-center justify-center">
              <Users className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-2">{totalBeneficiaries.toLocaleString()}</h3>
            <p className="text-gray-600">المستفيدون</p>
          </div>
        </div>

        {/* أدوات البحث والتصفية */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="ابحث في المشاريع..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
              />
            </div>

            <div>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
              >
                {statuses.map((status) => (
                  <option key={status} value={status}>
                    الحالة: {status}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
              >
                {categories.map((category) => (
                  <option key={category} value={category}>
                    الفئة: {category}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* قائمة المشاريع */}
        <div className="space-y-8">
          {filteredProjects.map((project) => (
            <div key={project.id} className="card hover:shadow-xl transition-shadow duration-300">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* معلومات المشروع */}
                <div className="lg:col-span-2 space-y-4">
                  <div className="flex items-start justify-between">
                    <h3 className="text-xl font-bold text-gray-900">{project.title}</h3>
                    <div className="flex items-center space-x-2 space-x-reverse">
                      {getStatusIcon(project.status)}
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(project.status)}`}>
                        {project.status}
                      </span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 leading-relaxed">{project.description}</p>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">الفئة:</span>
                      <p className="font-medium">{project.category}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">المقاول:</span>
                      <p className="font-medium">{project.contractor}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">تاريخ البداية:</span>
                      <p className="font-medium">{project.startDate}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">تاريخ الانتهاء:</span>
                      <p className="font-medium">{project.endDate}</p>
                    </div>
                  </div>

                  {/* الوثائق */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">الوثائق المتاحة:</h4>
                    <div className="flex flex-wrap gap-2">
                      {project.documents.map((doc, index) => (
                        <button
                          key={index}
                          className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                        >
                          <Download className="w-4 h-4 ml-1" />
                          {doc}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* إحصائيات المشروع */}
                <div className="space-y-6">
                  {/* شريط التقدم */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">التقدم</span>
                      <span className="text-sm font-bold text-iraqi-blue">{project.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div 
                        className="bg-iraqi-blue h-3 rounded-full transition-all duration-300"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* الميزانية */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">الميزانية الإجمالية</span>
                      <DollarSign className="w-4 h-4 text-iraqi-gold" />
                    </div>
                    <p className="text-lg font-bold text-gray-900">
                      {project.budget.toLocaleString()} دينار
                    </p>
                  </div>

                  {/* المستفيدون */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">عدد المستفيدين</span>
                      <Users className="w-4 h-4 text-iraqi-green" />
                    </div>
                    <p className="text-lg font-bold text-gray-900">
                      {project.beneficiaries.toLocaleString()} مواطن
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* رسالة عدم وجود نتائج */}
        {filteredProjects.length === 0 && (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
              <Search className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد مشاريع</h3>
            <p className="text-gray-600">
              لم يتم العثور على مشاريع تطابق معايير البحث المحددة
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default Projects
