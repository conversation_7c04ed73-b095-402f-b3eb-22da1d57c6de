import React, { useState, useEffect } from 'react'
import {
  GraduationCap,
  Briefcase,
  Award,
  Target,
  Heart,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Star,
  Users,
  BookOpen,
  Lightbulb,
  Shield,
  Globe,
  TrendingUp,
  CheckCircle,
  Eye,
  Download,
  Share2,
  Quote,
  Clock,
  Building,
  Flag
} from 'lucide-react'

const Biography = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [isVisible, setIsVisible] = useState({})

  // تأثيرات الحركة عند التمرير
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(prev => ({
              ...prev,
              [entry.target.id]: true
            }))
          }
        })
      },
      { threshold: 0.1 }
    )

    const elements = document.querySelectorAll('[data-animate]')
    elements.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  const personalInfo = {
    name: 'د. أحمد محمد العراقي',
    position: 'عضو مجلس النواب العراقي',
    constituency: 'محافظة بغداد - الدائرة الأولى',
    experience: '15 سنة',
    birthDate: '15 مارس 1975',
    birthPlace: 'بغداد، العراق',
    phone: '+964-************',
    email: '<EMAIL>',
    website: 'www.ahmed-iraqi.com',
    socialMedia: {
      facebook: '@AhmedIraqiOfficial',
      twitter: '@AhmedIraqi',
      instagram: '@ahmed_iraqi_official'
    },
    languages: ['العربية (اللغة الأم)', 'الإنجليزية (متقدم)', 'الكردية (متوسط)'],
    committees: [
      'لجنة الخدمات والإعمار (رئيس)',
      'لجنة القانون والعدالة (عضو)',
      'لجنة الشؤون الخارجية (عضو)',
      'لجنة مكافحة الفساد (عضو)'
    ]
  }

  const education = [
    {
      id: 1,
      degree: 'دكتوراه في القانون الدستوري',
      institution: 'جامعة بغداد - كلية القانون',
      year: '2005',
      gpa: '3.95/4.0',
      thesis: 'الرقابة الدستورية في النظام الفيدرالي العراقي',
      supervisor: 'أ.د. محمد علي الحكيم',
      description: 'تخصص في القانون الدستوري والإداري مع التركيز على النظم الفيدرالية',
      honors: ['مرتبة الشرف الأولى', 'جائزة أفضل أطروحة دكتوراه'],
      duration: '4 سنوات'
    },
    {
      id: 2,
      degree: 'ماجستير في العلوم السياسية',
      institution: 'الجامعة المستنصرية - كلية العلوم السياسية',
      year: '2000',
      gpa: '3.88/4.0',
      thesis: 'التحول الديمقراطي في العراق: التحديات والفرص',
      supervisor: 'أ.د. فاطمة الزهراء',
      description: 'تخصص في النظم السياسية المقارنة والتحول الديمقراطي',
      honors: ['تقدير امتياز', 'منحة التفوق الأكاديمي'],
      duration: 'سنتان'
    },
    {
      id: 3,
      degree: 'بكالوريوس في القانون',
      institution: 'جامعة بغداد - كلية القانون',
      year: '1998',
      gpa: '3.92/4.0',
      description: 'تخرج بتقدير امتياز مع مرتبة الشرف الأولى',
      honors: ['الأول على الدفعة', 'جائزة التفوق الأكاديمي', 'عضو جمعية الشرف'],
      duration: '4 سنوات',
      activities: ['رئيس اتحاد الطلبة', 'عضو فريق المناظرات القانونية']
    },
    {
      id: 4,
      degree: 'دبلوم في الإدارة العامة',
      institution: 'معهد الإدارة العامة - بغداد',
      year: '2003',
      description: 'برنامج تدريبي متخصص في الإدارة الحكومية والسياسات العامة',
      duration: '6 أشهر'
    }
  ]

  const experience = [
    {
      id: 1,
      position: 'عضو مجلس النواب العراقي',
      organization: 'البرلمان العراقي',
      period: '2010 - الآن',
      location: 'بغداد، العراق',
      type: 'منصب منتخب',
      description: 'عضو فعال في البرلمان العراقي مع التركيز على التشريع والرقابة البرلمانية',
      responsibilities: [
        'رئيس لجنة الخدمات والإعمار',
        'عضو لجنة القانون والعدالة',
        'عضو لجنة الشؤون الخارجية',
        'مقرر لجنة مكافحة الفساد'
      ],
      achievements: [
        'إقرار 15 قانون مهم لخدمة المواطنين',
        'الإشراف على 50+ مشروع تنموي',
        'تقديم 200+ استجواب ومساءلة برلمانية'
      ]
    },
    {
      id: 2,
      position: 'مستشار قانوني أول',
      organization: 'وزارة العدل العراقية',
      period: '2006 - 2010',
      location: 'بغداد، العراق',
      type: 'منصب حكومي',
      description: 'مستشار في الشؤون القانونية والدستورية مع التركيز على إصلاح النظام القضائي',
      responsibilities: [
        'مراجعة وصياغة مشاريع القوانين',
        'تقديم الاستشارات القانونية للوزير',
        'الإشراف على فريق المستشارين القانونيين',
        'التنسيق مع الجهات القضائية'
      ],
      achievements: [
        'المساهمة في صياغة 8 قوانين مهمة',
        'تطوير نظام إدارة القضايا الإلكتروني',
        'تدريب 100+ موظف قانوني'
      ]
    },
    {
      id: 3,
      position: 'أستاذ مساعد - القانون الدستوري',
      organization: 'جامعة بغداد - كلية القانون',
      period: '2005 - 2015',
      location: 'بغداد، العراق',
      type: 'منصب أكاديمي',
      description: 'تدريس القانون الدستوري والإداري للطلاب الجامعيين وطلاب الدراسات العليا',
      responsibilities: [
        'تدريس مواد القانون الدستوري والإداري',
        'الإشراف على رسائل الماجستير والدكتوراه',
        'إجراء البحوث القانونية',
        'المشاركة في المؤتمرات العلمية'
      ],
      achievements: [
        'تدريس 500+ طالب',
        'الإشراف على 25 رسالة ماجستير',
        'نشر 15 بحث علمي محكم'
      ]
    },
    {
      id: 4,
      position: 'محامي ومستشار قانوني',
      organization: 'مكتب المحاماة الخاص',
      period: '1998 - 2006',
      location: 'بغداد، العراق',
      type: 'عمل حر',
      description: 'ممارسة المحاماة في القضايا المدنية والإدارية والدستورية',
      responsibilities: [
        'تمثيل الموكلين أمام المحاكم',
        'تقديم الاستشارات القانونية',
        'صياغة العقود والاتفاقيات',
        'التحكيم في النزاعات التجارية'
      ],
      achievements: [
        'كسب 95% من القضايا المترافع بها',
        'تمثيل 200+ موكل',
        'تأسيس شراكة قانونية ناجحة'
      ]
    }
  ]

  const achievements = [
    {
      id: 1,
      title: 'جائزة أفضل نائب للعام 2023',
      organization: 'منظمة الشفافية العراقية',
      year: '2023',
      category: 'جائزة وطنية',
      description: 'تقديراً للجهود المتميزة في مكافحة الفساد وتعزيز الشفافية في العمل البرلماني',
      impact: 'تم اختياره من بين 329 نائب على مستوى العراق',
      certificate: true
    },
    {
      id: 2,
      title: 'وسام الخدمة المتميزة من الدرجة الأولى',
      organization: 'رئاسة الجمهورية العراقية',
      year: '2022',
      category: 'وسام حكومي',
      description: 'تقديراً للخدمات الجليلة المقدمة للمواطنين والوطن',
      impact: 'أعلى وسام يمنح للخدمة المدنية في العراق',
      certificate: true
    },
    {
      id: 3,
      title: 'جائزة التميز في التشريع',
      organization: 'نقابة المحامين العراقيين',
      year: '2021',
      category: 'جائزة مهنية',
      description: 'للمساهمة الفعالة في تطوير التشريعات القانونية وحماية حقوق المواطنين',
      impact: 'تقدير من أكبر نقابة مهنية في العراق',
      certificate: true
    },
    {
      id: 4,
      title: 'شهادة تقدير للعمل الإنساني',
      organization: 'الهلال الأحمر العراقي',
      year: '2020',
      category: 'عمل إنساني',
      description: 'للجهود المبذولة في مساعدة النازحين وضحايا الإرهاب',
      impact: 'مساعدة أكثر من 1000 عائلة نازحة',
      certificate: true
    },
    {
      id: 5,
      title: 'جائزة أفضل بحث قانوني',
      organization: 'المجمع العلمي العراقي',
      year: '2019',
      category: 'جائزة علمية',
      description: 'عن بحث "الفيدرالية والحكم المحلي في العراق"',
      impact: 'تم اعتماد البحث كمرجع في الجامعات العراقية',
      certificate: true
    }
  ]

  const skills = [
    {
      category: 'المهارات القانونية',
      items: [
        { name: 'القانون الدستوري', level: 95 },
        { name: 'القانون الإداري', level: 90 },
        { name: 'التشريع البرلماني', level: 92 },
        { name: 'المرافعات القانونية', level: 88 }
      ]
    },
    {
      category: 'المهارات السياسية',
      items: [
        { name: 'التفاوض السياسي', level: 90 },
        { name: 'إدارة الأزمات', level: 85 },
        { name: 'التواصل الجماهيري', level: 88 },
        { name: 'بناء التحالفات', level: 87 }
      ]
    },
    {
      category: 'المهارات الإدارية',
      items: [
        { name: 'القيادة والإدارة', level: 92 },
        { name: 'إدارة المشاريع', level: 85 },
        { name: 'التخطيط الاستراتيجي', level: 88 },
        { name: 'إدارة الفرق', level: 90 }
      ]
    }
  ]

  const publications = [
    {
      id: 1,
      title: 'الفيدرالية والحكم المحلي في العراق: دراسة مقارنة',
      type: 'كتاب',
      publisher: 'دار الحكمة للنشر',
      year: '2019',
      pages: 350,
      isbn: '978-964-123-456-7'
    },
    {
      id: 2,
      title: 'التحديات الدستورية في العراق الجديد',
      type: 'بحث محكم',
      publisher: 'مجلة القانون العراقية',
      year: '2018',
      pages: 45
    },
    {
      id: 3,
      title: 'دور البرلمان في الرقابة على السلطة التنفيذية',
      type: 'بحث محكم',
      publisher: 'مجلة الدراسات السياسية',
      year: '2017',
      pages: 32
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-iraqi-blue via-blue-800 to-iraqi-blue text-white py-20 overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="absolute inset-0">
          <div className="absolute top-10 right-10 w-32 h-32 bg-iraqi-gold opacity-10 rounded-full"></div>
          <div className="absolute bottom-10 left-10 w-24 h-24 bg-iraqi-green opacity-10 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white opacity-5 rounded-full"></div>
        </div>

        <div className="container-custom relative z-10">
          <div className="text-center mb-12">
            <div className="w-40 h-40 bg-white bg-opacity-20 rounded-full mx-auto mb-6 flex items-center justify-center backdrop-blur-sm">
              <span className="text-white text-6xl font-bold">د</span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-4">{personalInfo.name}</h1>
            <p className="text-2xl text-blue-100 mb-6">{personalInfo.position}</p>
            <div className="flex flex-wrap justify-center gap-4 text-lg">
              <span className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                <Calendar className="w-5 h-5 inline ml-2" />
                {personalInfo.experience} خبرة
              </span>
              <span className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                <MapPin className="w-5 h-5 inline ml-2" />
                {personalInfo.constituency}
              </span>
              <span className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
                <Users className="w-5 h-5 inline ml-2" />
                خدمة المواطنين
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Navigation Tabs */}
      <section className="bg-white shadow-sm sticky top-0 z-40">
        <div className="container-custom">
          <div className="flex flex-wrap justify-center gap-2 py-4">
            {[
              { id: 'overview', label: 'نظرة عامة', icon: Eye },
              { id: 'education', label: 'التعليم', icon: GraduationCap },
              { id: 'experience', label: 'الخبرة', icon: Briefcase },
              { id: 'achievements', label: 'الإنجازات', icon: Award },
              { id: 'skills', label: 'المهارات', icon: Star },
              { id: 'publications', label: 'المؤلفات', icon: BookOpen }
            ].map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-6 py-3 rounded-full transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-iraqi-blue text-white shadow-lg'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <IconComponent className="w-5 h-5 ml-2" />
                  {tab.label}
                </button>
              )
            })}
          </div>
        </div>
      </section>

      <div className="py-12">
        <div className="container-custom">

          {/* نظرة عامة */}
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12" data-animate id="overview">
              {/* المعلومات الشخصية */}
              <div className="lg:col-span-1">
                <div className="card sticky top-24">
                  <div className="text-center mb-6">
                    <div className="w-32 h-32 bg-gradient-to-br from-iraqi-gold to-yellow-600 rounded-full mx-auto mb-4 flex items-center justify-center shadow-lg">
                      <span className="text-white text-4xl font-bold">د</span>
                    </div>
                    <h2 className="text-2xl font-bold text-gray-900">{personalInfo.name}</h2>
                    <p className="text-iraqi-blue font-medium">{personalInfo.position}</p>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <MapPin className="w-5 h-5 text-iraqi-blue" />
                      <div>
                        <p className="font-medium">الدائرة الانتخابية</p>
                        <p className="text-gray-600">{personalInfo.constituency}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Calendar className="w-5 h-5 text-iraqi-blue" />
                      <div>
                        <p className="font-medium">تاريخ الميلاد</p>
                        <p className="text-gray-600">{personalInfo.birthDate}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Flag className="w-5 h-5 text-iraqi-blue" />
                      <div>
                        <p className="font-medium">مكان الميلاد</p>
                        <p className="text-gray-600">{personalInfo.birthPlace}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Phone className="w-5 h-5 text-iraqi-blue" />
                      <div>
                        <p className="font-medium">الهاتف</p>
                        <p className="text-gray-600">{personalInfo.phone}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Mail className="w-5 h-5 text-iraqi-blue" />
                      <div>
                        <p className="font-medium">البريد الإلكتروني</p>
                        <p className="text-gray-600">{personalInfo.email}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 space-x-reverse">
                      <Globe className="w-5 h-5 text-iraqi-blue" />
                      <div>
                        <p className="font-medium">الموقع الإلكتروني</p>
                        <p className="text-gray-600">{personalInfo.website}</p>
                      </div>
                    </div>
                  </div>

                  {/* اللغات */}
                  <div className="mt-6 pt-6 border-t">
                    <h3 className="font-semibold text-gray-900 mb-3">اللغات</h3>
                    <div className="space-y-2">
                      {personalInfo.languages.map((lang, index) => (
                        <div key={index} className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded">
                          {lang}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* وسائل التواصل الاجتماعي */}
                  <div className="mt-6 pt-6 border-t">
                    <h3 className="font-semibold text-gray-900 mb-3">وسائل التواصل</h3>
                    <div className="space-y-2">
                      <div className="text-sm text-gray-600">
                        <strong>فيسبوك:</strong> {personalInfo.socialMedia.facebook}
                      </div>
                      <div className="text-sm text-gray-600">
                        <strong>تويتر:</strong> {personalInfo.socialMedia.twitter}
                      </div>
                      <div className="text-sm text-gray-600">
                        <strong>إنستغرام:</strong> {personalInfo.socialMedia.instagram}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* المحتوى الرئيسي */}
              <div className="lg:col-span-2 space-y-12">
                {/* الرؤية والرسالة */}
                <section>
                  <div className="flex items-center mb-6">
                    <Target className="w-6 h-6 text-iraqi-blue ml-3" />
                    <h2 className="text-2xl font-bold text-gray-900">الرؤية والرسالة</h2>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="card bg-gradient-to-br from-iraqi-blue to-blue-700 text-white">
                      <div className="flex items-center mb-4">
                        <Eye className="w-6 h-6 ml-3" />
                        <h3 className="text-xl font-semibold">الرؤية</h3>
                      </div>
                      <p className="leading-relaxed">
                        بناء عراق قوي ومزدهر يقوم على العدالة والشفافية والخدمة المتميزة للمواطنين،
                        حيث يتمتع كل مواطن بحقوقه كاملة ويساهم في بناء مستقبل أفضل للأجيال القادمة.
                      </p>
                    </div>
                    <div className="card bg-gradient-to-br from-iraqi-gold to-yellow-600 text-white">
                      <div className="flex items-center mb-4">
                        <Heart className="w-6 h-6 ml-3" />
                        <h3 className="text-xl font-semibold">الرسالة</h3>
                      </div>
                      <p className="leading-relaxed">
                        العمل بإخلاص وتفان لخدمة المواطنين العراقيين من خلال التشريع الفعال،
                        والرقابة البرلمانية النزيهة، وتقديم الخدمات بشفافية ومسؤولية عالية.
                      </p>
                    </div>
                  </div>
                </section>

                {/* اللجان البرلمانية */}
                <section>
                  <div className="flex items-center mb-6">
                    <Building className="w-6 h-6 text-iraqi-blue ml-3" />
                    <h2 className="text-2xl font-bold text-gray-900">اللجان البرلمانية</h2>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {personalInfo.committees.map((committee, index) => (
                      <div key={index} className="card hover:shadow-lg transition-shadow duration-300">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-iraqi-green rounded-full flex items-center justify-center ml-3">
                            <CheckCircle className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{committee}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </section>

                {/* إحصائيات سريعة */}
                <section>
                  <div className="flex items-center mb-6">
                    <TrendingUp className="w-6 h-6 text-iraqi-blue ml-3" />
                    <h2 className="text-2xl font-bold text-gray-900">إحصائيات سريعة</h2>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div className="card text-center bg-gradient-to-br from-iraqi-blue to-blue-700 text-white">
                      <div className="text-3xl font-bold mb-2">15+</div>
                      <div className="text-sm">سنوات خبرة</div>
                    </div>
                    <div className="card text-center bg-gradient-to-br from-iraqi-gold to-yellow-600 text-white">
                      <div className="text-3xl font-bold mb-2">50+</div>
                      <div className="text-sm">مشروع منجز</div>
                    </div>
                    <div className="card text-center bg-gradient-to-br from-iraqi-green to-green-600 text-white">
                      <div className="text-3xl font-bold mb-2">200+</div>
                      <div className="text-sm">استجواب برلماني</div>
                    </div>
                    <div className="card text-center bg-gradient-to-br from-iraqi-red to-red-600 text-white">
                      <div className="text-3xl font-bold mb-2">5</div>
                      <div className="text-sm">جوائز وتكريمات</div>
                    </div>
                  </div>
                </section>
              </div>
            </div>
          )}

          {/* التعليم */}
          {activeTab === 'education' && (
            <div className="space-y-8" data-animate id="education">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">المؤهلات العلمية</h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  مسيرة تعليمية متميزة في أرقى الجامعات العراقية مع التخصص في القانون والعلوم السياسية
                </p>
              </div>

              <div className="space-y-8">
                {education.map((edu, index) => (
                  <div key={edu.id} className="card hover:shadow-xl transition-all duration-300">
                    <div className="flex items-start space-x-6 space-x-reverse">
                      <div className="w-16 h-16 bg-gradient-to-br from-iraqi-blue to-blue-700 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                        <GraduationCap className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-1">{edu.degree}</h3>
                            <p className="text-iraqi-blue font-semibold text-lg">{edu.institution}</p>
                          </div>
                          <div className="text-left md:text-right">
                            <span className="bg-iraqi-gold text-white px-4 py-2 rounded-full text-sm font-medium">
                              {edu.year}
                            </span>
                            {edu.duration && (
                              <p className="text-gray-500 text-sm mt-1">{edu.duration}</p>
                            )}
                          </div>
                        </div>

                        <p className="text-gray-600 mb-4 leading-relaxed">{edu.description}</p>

                        {edu.gpa && (
                          <div className="mb-4">
                            <span className="text-sm font-medium text-gray-700">المعدل: </span>
                            <span className="text-iraqi-green font-bold">{edu.gpa}</span>
                          </div>
                        )}

                        {edu.thesis && (
                          <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                            <h4 className="font-semibold text-gray-900 mb-2">عنوان الأطروحة/الرسالة:</h4>
                            <p className="text-gray-700 italic">"{edu.thesis}"</p>
                            {edu.supervisor && (
                              <p className="text-gray-600 text-sm mt-2">
                                <strong>المشرف:</strong> {edu.supervisor}
                              </p>
                            )}
                          </div>
                        )}

                        {edu.honors && (
                          <div className="mb-4">
                            <h4 className="font-semibold text-gray-900 mb-2">التكريمات والجوائز:</h4>
                            <div className="flex flex-wrap gap-2">
                              {edu.honors.map((honor, honorIndex) => (
                                <span
                                  key={honorIndex}
                                  className="bg-iraqi-green text-white px-3 py-1 rounded-full text-sm"
                                >
                                  {honor}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                        {edu.activities && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">الأنشطة الطلابية:</h4>
                            <div className="flex flex-wrap gap-2">
                              {edu.activities.map((activity, activityIndex) => (
                                <span
                                  key={activityIndex}
                                  className="bg-gray-200 text-gray-700 px-3 py-1 rounded-full text-sm"
                                >
                                  {activity}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* الخبرة المهنية */}
          {activeTab === 'experience' && (
            <div className="space-y-8" data-animate id="experience">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">الخبرات المهنية</h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  مسيرة مهنية حافلة بالإنجازات في القطاعين العام والخاص والأكاديمي
                </p>
              </div>

              <div className="relative">
                {/* خط زمني */}
                <div className="absolute right-8 top-0 bottom-0 w-1 bg-iraqi-blue"></div>

                <div className="space-y-12">
                  {experience.map((exp, index) => (
                    <div key={exp.id} className="relative">
                      {/* نقطة على الخط الزمني */}
                      <div className="absolute right-6 w-5 h-5 bg-iraqi-gold rounded-full border-4 border-white shadow-lg"></div>

                      <div className="mr-16">
                        <div className="card hover:shadow-xl transition-all duration-300">
                          <div className="flex items-start space-x-6 space-x-reverse">
                            <div className="w-16 h-16 bg-gradient-to-br from-iraqi-gold to-yellow-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                              <Briefcase className="w-8 h-8 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                <div>
                                  <h3 className="text-xl font-bold text-gray-900 mb-1">{exp.position}</h3>
                                  <p className="text-iraqi-blue font-semibold text-lg">{exp.organization}</p>
                                  <p className="text-gray-500 text-sm">{exp.location}</p>
                                </div>
                                <div className="text-left md:text-right">
                                  <span className="bg-iraqi-blue text-white px-4 py-2 rounded-full text-sm font-medium">
                                    {exp.period}
                                  </span>
                                  <p className="text-gray-500 text-sm mt-1">{exp.type}</p>
                                </div>
                              </div>

                              <p className="text-gray-600 mb-6 leading-relaxed">{exp.description}</p>

                              {exp.responsibilities && (
                                <div className="mb-6">
                                  <h4 className="font-semibold text-gray-900 mb-3">المسؤوليات الرئيسية:</h4>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    {exp.responsibilities.map((responsibility, respIndex) => (
                                      <div key={respIndex} className="flex items-center">
                                        <CheckCircle className="w-4 h-4 text-iraqi-green ml-2 flex-shrink-0" />
                                        <span className="text-gray-700 text-sm">{responsibility}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {exp.achievements && (
                                <div>
                                  <h4 className="font-semibold text-gray-900 mb-3">الإنجازات البارزة:</h4>
                                  <div className="space-y-2">
                                    {exp.achievements.map((achievement, achIndex) => (
                                      <div key={achIndex} className="flex items-center">
                                        <Star className="w-4 h-4 text-iraqi-gold ml-2 flex-shrink-0" />
                                        <span className="text-gray-700 text-sm">{achievement}</span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* الإنجازات والجوائز */}
          {activeTab === 'achievements' && (
            <div className="space-y-8" data-animate id="achievements">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">الإنجازات والجوائز</h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  تقدير وطني ودولي للجهود المبذولة في خدمة الوطن والمواطنين
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {achievements.map((achievement, index) => (
                  <div key={achievement.id} className="card hover:shadow-xl transition-all duration-300 group">
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="w-16 h-16 bg-gradient-to-br from-iraqi-green to-green-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <Award className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-3">
                          <span className="bg-iraqi-gold text-white px-3 py-1 rounded-full text-sm font-medium">
                            {achievement.year}
                          </span>
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {achievement.category}
                          </span>
                        </div>

                        <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-iraqi-blue transition-colors duration-300">
                          {achievement.title}
                        </h3>

                        <p className="text-iraqi-blue font-semibold mb-3">{achievement.organization}</p>

                        <p className="text-gray-600 mb-4 leading-relaxed">{achievement.description}</p>

                        {achievement.impact && (
                          <div className="bg-gray-50 p-3 rounded-lg mb-3">
                            <p className="text-sm text-gray-700">
                              <strong>التأثير:</strong> {achievement.impact}
                            </p>
                          </div>
                        )}

                        {achievement.certificate && (
                          <div className="flex items-center text-sm text-iraqi-green">
                            <CheckCircle className="w-4 h-4 ml-1" />
                            شهادة معتمدة
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* المهارات */}
          {activeTab === 'skills' && (
            <div className="space-y-8" data-animate id="skills">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">المهارات والاختصاصات</h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  مهارات متنوعة ومتطورة في مختلف المجالات القانونية والسياسية والإدارية
                </p>
              </div>

              <div className="space-y-8">
                {skills.map((skillCategory, index) => (
                  <div key={index} className="card">
                    <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                      <Lightbulb className="w-6 h-6 text-iraqi-blue ml-3" />
                      {skillCategory.category}
                    </h3>
                    <div className="space-y-4">
                      {skillCategory.items.map((skill, skillIndex) => (
                        <div key={skillIndex}>
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-gray-900">{skill.name}</span>
                            <span className="text-sm text-gray-600">{skill.level}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div
                              className="bg-gradient-to-r from-iraqi-blue to-iraqi-gold h-3 rounded-full transition-all duration-1000 ease-out"
                              style={{ width: `${skill.level}%` }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* المؤلفات */}
          {activeTab === 'publications' && (
            <div className="space-y-8" data-animate id="publications">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">المؤلفات والبحوث</h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  إسهامات علمية وبحثية في مجال القانون والعلوم السياسية
                </p>
              </div>

              <div className="space-y-6">
                {publications.map((publication, index) => (
                  <div key={publication.id} className="card hover:shadow-xl transition-all duration-300">
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="w-16 h-16 bg-gradient-to-br from-iraqi-blue to-blue-700 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                        <BookOpen className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">{publication.title}</h3>
                            <p className="text-iraqi-blue font-semibold">{publication.publisher}</p>
                          </div>
                          <div className="text-left md:text-right">
                            <span className="bg-iraqi-gold text-white px-4 py-2 rounded-full text-sm font-medium">
                              {publication.year}
                            </span>
                            <p className="text-gray-500 text-sm mt-1">{publication.type}</p>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                          <span>
                            <strong>عدد الصفحات:</strong> {publication.pages}
                          </span>
                          {publication.isbn && (
                            <span>
                              <strong>ISBN:</strong> {publication.isbn}
                            </span>
                          )}
                        </div>

                        <div className="flex gap-3 mt-4">
                          <button className="flex items-center px-4 py-2 bg-iraqi-blue text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                            <Download className="w-4 h-4 ml-2" />
                            تحميل
                          </button>
                          <button className="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors duration-300">
                            <Share2 className="w-4 h-4 ml-2" />
                            مشاركة
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Biography
