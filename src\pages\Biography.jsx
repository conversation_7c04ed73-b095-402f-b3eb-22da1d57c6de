import React from 'react'
import { 
  GraduationCap, 
  Briefcase, 
  Award, 
  Target, 
  Heart,
  Calendar,
  MapPin,
  Phone,
  Mail
} from 'lucide-react'

const Biography = () => {
  const personalInfo = {
    name: 'النائب العراقي',
    position: 'عضو مجلس النواب العراقي',
    constituency: 'محافظة بغداد',
    experience: '15 سنة',
    birthDate: '1975',
    birthPlace: 'بغداد، العراق',
    phone: '+964-XXX-XXXX-XXX',
    email: '<EMAIL>'
  }

  const education = [
    {
      degree: 'دكتوراه في القانون الدستوري',
      institution: 'جامعة بغداد - كلية القانون',
      year: '2005',
      description: 'تخصص في القانون الدستوري والإداري'
    },
    {
      degree: 'ماجستير في العلوم السياسية',
      institution: 'الجامعة المستنصرية',
      year: '2000',
      description: 'تخصص في النظم السياسية المقارنة'
    },
    {
      degree: 'بكالوريوس في القانون',
      institution: 'جامعة بغداد - كلية القانون',
      year: '1998',
      description: 'تخرج بتقدير امتياز مع مرتبة الشرف'
    }
  ]

  const experience = [
    {
      position: 'عضو مجلس النواب العراقي',
      organization: 'البرلمان العراقي',
      period: '2010 - الآن',
      description: 'عضو في لجنة الخدمات والإعمار ولجنة القانون'
    },
    {
      position: 'مستشار قانوني',
      organization: 'وزارة العدل',
      period: '2006 - 2010',
      description: 'مستشار في الشؤون القانونية والدستورية'
    },
    {
      position: 'محاضر جامعي',
      organization: 'جامعة بغداد',
      period: '2005 - 2010',
      description: 'تدريس القانون الدستوري والإداري'
    }
  ]

  const achievements = [
    {
      title: 'جائزة أفضل نائب للعام 2023',
      organization: 'منظمة الشفافية العراقية',
      year: '2023',
      description: 'تقديراً للجهود في مكافحة الفساد'
    },
    {
      title: 'وسام الخدمة المتميزة',
      organization: 'رئاسة الجمهورية',
      year: '2022',
      description: 'تقديراً للخدمات المقدمة للمواطنين'
    },
    {
      title: 'شهادة تقدير من نقابة المحامين',
      organization: 'نقابة المحامين العراقيين',
      year: '2021',
      description: 'للمساهمة في تطوير التشريعات القانونية'
    }
  ]

  return (
    <div className="min-h-screen py-12">
      <div className="container-custom">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">السيرة الذاتية</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            تعرف على مسيرة النائب العراقي ومؤهلاته وخبراته في خدمة الوطن والمواطنين
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* المعلومات الشخصية */}
          <div className="lg:col-span-1">
            <div className="card sticky top-8">
              <div className="text-center mb-6">
                <div className="w-32 h-32 bg-iraqi-gold rounded-full mx-auto mb-4 flex items-center justify-center">
                  <span className="text-white text-4xl font-bold">ن</span>
                </div>
                <h2 className="text-2xl font-bold text-gray-900">{personalInfo.name}</h2>
                <p className="text-iraqi-blue font-medium">{personalInfo.position}</p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <MapPin className="w-5 h-5 text-iraqi-blue" />
                  <div>
                    <p className="font-medium">الدائرة الانتخابية</p>
                    <p className="text-gray-600">{personalInfo.constituency}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3 space-x-reverse">
                  <Calendar className="w-5 h-5 text-iraqi-blue" />
                  <div>
                    <p className="font-medium">سنوات الخبرة</p>
                    <p className="text-gray-600">{personalInfo.experience}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 space-x-reverse">
                  <Phone className="w-5 h-5 text-iraqi-blue" />
                  <div>
                    <p className="font-medium">الهاتف</p>
                    <p className="text-gray-600">{personalInfo.phone}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-3 space-x-reverse">
                  <Mail className="w-5 h-5 text-iraqi-blue" />
                  <div>
                    <p className="font-medium">البريد الإلكتروني</p>
                    <p className="text-gray-600">{personalInfo.email}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="lg:col-span-2 space-y-12">
            {/* الرؤية والرسالة */}
            <section>
              <div className="flex items-center mb-6">
                <Target className="w-6 h-6 text-iraqi-blue ml-3" />
                <h2 className="text-2xl font-bold text-gray-900">الرؤية والرسالة</h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="card">
                  <h3 className="text-lg font-semibold text-iraqi-blue mb-3">الرؤية</h3>
                  <p className="text-gray-600 leading-relaxed">
                    بناء عراق قوي ومزدهر يقوم على العدالة والشفافية والخدمة المتميزة للمواطنين، 
                    حيث يتمتع كل مواطن بحقوقه كاملة ويساهم في بناء مستقبل أفضل للأجيال القادمة.
                  </p>
                </div>
                <div className="card">
                  <h3 className="text-lg font-semibold text-iraqi-blue mb-3">الرسالة</h3>
                  <p className="text-gray-600 leading-relaxed">
                    العمل بإخلاص وتفان لخدمة المواطنين العراقيين من خلال التشريع الفعال، 
                    والرقابة البرلمانية النزيهة، وتقديم الخدمات بشفافية ومسؤولية عالية.
                  </p>
                </div>
              </div>
            </section>

            {/* المؤهلات العلمية */}
            <section>
              <div className="flex items-center mb-6">
                <GraduationCap className="w-6 h-6 text-iraqi-blue ml-3" />
                <h2 className="text-2xl font-bold text-gray-900">المؤهلات العلمية</h2>
              </div>
              <div className="space-y-6">
                {education.map((edu, index) => (
                  <div key={index} className="card">
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="w-12 h-12 bg-iraqi-blue rounded-full flex items-center justify-center flex-shrink-0">
                        <GraduationCap className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{edu.degree}</h3>
                        <p className="text-iraqi-blue font-medium">{edu.institution}</p>
                        <p className="text-gray-500 text-sm mb-2">{edu.year}</p>
                        <p className="text-gray-600">{edu.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* الخبرات المهنية */}
            <section>
              <div className="flex items-center mb-6">
                <Briefcase className="w-6 h-6 text-iraqi-blue ml-3" />
                <h2 className="text-2xl font-bold text-gray-900">الخبرات المهنية</h2>
              </div>
              <div className="space-y-6">
                {experience.map((exp, index) => (
                  <div key={index} className="card">
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="w-12 h-12 bg-iraqi-gold rounded-full flex items-center justify-center flex-shrink-0">
                        <Briefcase className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{exp.position}</h3>
                        <p className="text-iraqi-blue font-medium">{exp.organization}</p>
                        <p className="text-gray-500 text-sm mb-2">{exp.period}</p>
                        <p className="text-gray-600">{exp.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>

            {/* الإنجازات والجوائز */}
            <section>
              <div className="flex items-center mb-6">
                <Award className="w-6 h-6 text-iraqi-blue ml-3" />
                <h2 className="text-2xl font-bold text-gray-900">الإنجازات والجوائز</h2>
              </div>
              <div className="space-y-6">
                {achievements.map((achievement, index) => (
                  <div key={index} className="card">
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className="w-12 h-12 bg-iraqi-green rounded-full flex items-center justify-center flex-shrink-0">
                        <Award className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900">{achievement.title}</h3>
                        <p className="text-iraqi-blue font-medium">{achievement.organization}</p>
                        <p className="text-gray-500 text-sm mb-2">{achievement.year}</p>
                        <p className="text-gray-600">{achievement.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Biography
