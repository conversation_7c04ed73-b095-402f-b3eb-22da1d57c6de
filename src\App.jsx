import React from 'react'
import { HashRouter as Router, Routes, Route } from 'react-router-dom'
import Layout from './components/Layout/Layout'
import Home from './pages/Home'
import Biography from './pages/Biography'
import News from './pages/News'
import Projects from './pages/Projects'
import Gallery from './pages/Gallery'
import Support from './pages/Support'
import Contact from './pages/Contact'
import BioTree from './pages/BioTree'
import AdminLayout from './components/Admin/AdminLayout'
import AdminDashboard from './pages/Admin/Dashboard'
import AdminNews from './pages/Admin/News'
import AdminProjects from './pages/Admin/Projects'
import AdminGallery from './pages/Admin/Gallery'
import AdminMessages from './pages/Admin/Messages'
import AdminProfile from './pages/Admin/Profile'
import AdminBioTree from './pages/Admin/BioTree'
import AdminSettings from './pages/Admin/Settings'
import AdminLogin from './pages/Admin/Login'
import { AuthProvider } from './contexts/AuthContext'

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            {/* الصفحات العامة */}
            <Route path="/" element={<Layout />}>
              <Route index element={<Home />} />
              <Route path="biography" element={<Biography />} />
              <Route path="news" element={<News />} />
              <Route path="projects" element={<Projects />} />
              <Route path="gallery" element={<Gallery />} />
              <Route path="support" element={<Support />} />
              <Route path="contact" element={<Contact />} />
              <Route path="biotree" element={<BioTree />} />
            </Route>
            
            {/* صفحات الإدارة */}
            <Route path="/admin/login" element={<AdminLogin />} />
            <Route path="/admin" element={<AdminLayout />}>
              <Route index element={<AdminDashboard />} />
              <Route path="news" element={<AdminNews />} />
              <Route path="projects" element={<AdminProjects />} />
              <Route path="gallery" element={<AdminGallery />} />
              <Route path="messages" element={<AdminMessages />} />
              <Route path="profile" element={<AdminProfile />} />
              <Route path="biotree" element={<AdminBioTree />} />
              <Route path="settings" element={<AdminSettings />} />
            </Route>
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  )
}

export default App
