@tailwind base;
@tailwind components;
@tailwind utilities;

/* تخصيصات CSS للعربية */
@layer base {
  html {
    direction: rtl;
  }
  
  body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    line-height: 1.6;
  }
  
  /* تحسين عرض النصوص العربية */
  .arabic-text {
    font-feature-settings: "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
  }
  
  /* تخصيص الاتجاه للعناصر المختلطة */
  .ltr {
    direction: ltr;
  }
  
  .rtl {
    direction: rtl;
  }
}

@layer components {
  /* أزرار مخصصة */
  .btn-primary {
    @apply bg-iraqi-blue text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-iraqi-gold text-white px-6 py-3 rounded-lg font-semibold hover:bg-yellow-600 transition-colors duration-200;
  }
  
  .btn-outline {
    @apply border-2 border-iraqi-blue text-iraqi-blue px-6 py-3 rounded-lg font-semibold hover:bg-iraqi-blue hover:text-white transition-all duration-200;
  }
  
  /* بطاقات */
  .card {
    @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200;
  }
  
  .card-header {
    @apply border-b border-gray-200 pb-4 mb-4;
  }
  
  /* تخطيط الشبكة */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* تأثيرات الحركة */
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
}

@layer utilities {
  /* مساعدات RTL */
  .mr-auto-rtl {
    margin-right: auto;
    margin-left: 0;
  }
  
  .ml-auto-rtl {
    margin-left: auto;
    margin-right: 0;
  }
  
  /* تخصيص التمرير */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* تحسين الأداء */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
}
