@tailwind base;
@tailwind components;
@tailwind utilities;

/* تخصيصات CSS للعربية */
@layer base {
  html {
    direction: rtl;
  }
  
  body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    line-height: 1.6;
  }
  
  /* تحسين عرض النصوص العربية */
  .arabic-text {
    font-feature-settings: "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
  }
  
  /* تخصيص الاتجاه للعناصر المختلطة */
  .ltr {
    direction: ltr;
  }
  
  .rtl {
    direction: rtl;
  }
}

@layer components {
  /* تخطيط الشبكة */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* فئات مساعدة للنصوص */
  .text-gradient-iraqi {
    @apply bg-gradient-to-r from-iraqi-blue to-iraqi-gold bg-clip-text text-transparent;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* تأثيرات الحركة */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
  
  /* تأثيرات الحركة */
  .fade-in {
    @apply animate-fade-in;
  }
  
  .slide-up {
    @apply animate-slide-up;
  }
}

@layer utilities {
  /* مساعدات RTL */
  .mr-auto-rtl {
    margin-right: auto;
    margin-left: 0;
  }
  
  .ml-auto-rtl {
    margin-left: auto;
    margin-right: 0;
  }
  
  /* تخصيص التمرير */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* تحسين الأداء */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
}
