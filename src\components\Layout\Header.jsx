import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { Menu, X, Phone, Mail, MapPin } from 'lucide-react'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const location = useLocation()

  const navigation = [
    { name: 'الرئيسية', href: '/' },
    { name: 'السيرة الذاتية', href: '/biography' },
    { name: 'الأخبار والأنشطة', href: '/news' },
    { name: 'المشاريع والإنجازات', href: '/projects' },
    { name: 'المعرض', href: '/gallery' },
    { name: 'دعم الناخبين', href: '/support' },
    { name: 'اتصل بي', href: '/contact' },
    { name: '<PERSON><PERSON> Tree', href: '/biotree' }
  ]

  const isActive = (path) => location.pathname === path

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      {/* شريط المعلومات العلوي */}
      <div className="bg-iraqi-blue text-white py-2">
        <div className="container-custom">
          <div className="flex flex-col sm:flex-row justify-between items-center text-sm">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex items-center">
                <Phone className="w-4 h-4 ml-2" />
                <span>+964-XXX-XXXX-XXX</span>
              </div>
              <div className="flex items-center">
                <Mail className="w-4 h-4 ml-2" />
                <span><EMAIL></span>
              </div>
            </div>
            <div className="flex items-center mt-2 sm:mt-0">
              <MapPin className="w-4 h-4 ml-2" />
              <span>بغداد، العراق</span>
            </div>
          </div>
        </div>
      </div>

      {/* الشريط الرئيسي */}
      <div className="container-custom">
        <div className="flex justify-between items-center py-4">
          {/* الشعار */}
          <Link to="/" className="flex items-center space-x-3 space-x-reverse">
            <div className="w-12 h-12 bg-iraqi-gold rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-xl">ن</span>
            </div>
            <div>
              <h1 className="text-xl font-bold text-iraqi-blue">النائب العراقي</h1>
              <p className="text-sm text-gray-600">خدمة المواطن أولاً</p>
            </div>
          </Link>

          {/* القائمة الرئيسية - سطح المكتب */}
          <nav className="hidden lg:flex space-x-8 space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  isActive(item.href)
                    ? 'text-iraqi-blue bg-blue-50'
                    : 'text-gray-700 hover:text-iraqi-blue hover:bg-gray-50'
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* زر القائمة - الهاتف المحمول */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="lg:hidden p-2 rounded-md text-gray-700 hover:text-iraqi-blue hover:bg-gray-50"
          >
            {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* القائمة المنسدلة - الهاتف المحمول */}
        {isMenuOpen && (
          <div className="lg:hidden border-t border-gray-200">
            <nav className="py-4 space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 ${
                    isActive(item.href)
                      ? 'text-iraqi-blue bg-blue-50'
                      : 'text-gray-700 hover:text-iraqi-blue hover:bg-gray-50'
                  }`}
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
