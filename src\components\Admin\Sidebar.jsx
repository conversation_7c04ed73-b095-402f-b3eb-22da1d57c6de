import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  LayoutDashboard, 
  Newspaper, 
  FolderOpen, 
  Image, 
  MessageSquare, 
  User, 
  Link as LinkIcon, 
  Settings,
  ChevronLeft,
  ChevronRight,
  LogOut
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { Button } from '../UI'

const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const location = useLocation()
  const { logout, user } = useAuth()

  const navigation = [
    {
      name: 'الداشبورد',
      href: '/admin',
      icon: LayoutDashboard,
      exact: true
    },
    {
      name: 'إدارة الأخبار',
      href: '/admin/news',
      icon: Newspaper
    },
    {
      name: 'إدارة المشاريع',
      href: '/admin/projects',
      icon: FolderOpen
    },
    {
      name: 'إدارة المعرض',
      href: '/admin/gallery',
      icon: Image
    },
    {
      name: 'الرسائل',
      href: '/admin/messages',
      icon: MessageSquare
    },
    {
      name: 'الملف الشخصي',
      href: '/admin/profile',
      icon: User
    },
    {
      name: 'Bio Tree',
      href: '/admin/biotree',
      icon: LinkIcon
    },
    {
      name: 'الإعدادات',
      href: '/admin/settings',
      icon: Settings
    }
  ]

  const isActive = (path, exact = false) => {
    if (exact) {
      return location.pathname === path
    }
    return location.pathname.startsWith(path)
  }

  const handleLogout = () => {
    logout()
  }

  return (
    <div className={`bg-white shadow-lg transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } flex flex-col`}>
      {/* رأس الشريط الجانبي */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-iraqi-blue rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">ن</span>
              </div>
              <div>
                <h2 className="text-lg font-bold text-gray-900">لوحة التحكم</h2>
                <p className="text-sm text-gray-500">النائب العراقي</p>
              </div>
            </div>
          )}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            {isCollapsed ? (
              <ChevronLeft className="w-5 h-5 text-gray-600" />
            ) : (
              <ChevronRight className="w-5 h-5 text-gray-600" />
            )}
          </button>
        </div>
      </div>

      {/* معلومات المستخدم */}
      {!isCollapsed && (
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-3 space-x-reverse">
            <div className="w-8 h-8 bg-iraqi-gold rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.name || 'مدير النظام'}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user?.role || 'مدير'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* قائمة التنقل */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => {
          const Icon = item.icon
          const active = isActive(item.href, item.exact)
          
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`flex items-center space-x-3 space-x-reverse px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                active
                  ? 'bg-iraqi-blue text-white'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-iraqi-blue'
              }`}
              title={isCollapsed ? item.name : ''}
            >
              <Icon className={`w-5 h-5 ${active ? 'text-white' : 'text-gray-500'}`} />
              {!isCollapsed && <span>{item.name}</span>}
            </Link>
          )
        })}
      </nav>

      {/* زر تسجيل الخروج */}
      <div className="p-4 border-t border-gray-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          className={`w-full ${isCollapsed ? 'px-2' : 'px-3'} py-2 text-red-600 hover:bg-red-50 hover:text-red-700`}
        >
          <LogOut className="w-5 h-5" />
          {!isCollapsed && <span className="mr-2">تسجيل الخروج</span>}
        </Button>
      </div>
    </div>
  )
}

export default Sidebar
