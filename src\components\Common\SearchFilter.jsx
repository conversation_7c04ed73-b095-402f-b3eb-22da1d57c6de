import React from 'react'
import { Search, Filter, X } from 'lucide-react'
import { Input, Select, Button } from '../UI'

const SearchFilter = ({
  searchValue = '',
  onSearchChange,
  searchPlaceholder = 'البحث...',
  categories = [],
  selectedCategory = '',
  onCategoryChange,
  sortOptions = [],
  selectedSort = '',
  onSortChange,
  showClearButton = true,
  onClear,
  className = '',
  ...props
}) => {
  const handleClear = () => {
    onSearchChange?.('')
    onCategoryChange?.('')
    onSortChange?.('')
    onClear?.()
  }
  
  const hasActiveFilters = searchValue || selectedCategory || selectedSort
  
  return (
    <div className={`bg-white rounded-lg shadow-md p-6 mb-6 ${className}`} {...props}>
      <div className="flex flex-col lg:flex-row gap-4">
        {/* البحث */}
        <div className="flex-1">
          <Input
            type="text"
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => onSearchChange?.(e.target.value)}
            icon={Search}
            iconPosition="right"
            size="md"
          />
        </div>
        
        {/* فلتر الفئات */}
        {categories.length > 0 && (
          <div className="lg:w-48">
            <Select
              placeholder="جميع الفئات"
              value={selectedCategory}
              onChange={(e) => onCategoryChange?.(e.target.value)}
              options={[
                { value: '', label: 'جميع الفئات' },
                ...categories.map(cat => ({
                  value: cat.value || cat,
                  label: cat.label || cat
                }))
              ]}
              size="md"
            />
          </div>
        )}
        
        {/* ترتيب */}
        {sortOptions.length > 0 && (
          <div className="lg:w-48">
            <Select
              placeholder="ترتيب حسب"
              value={selectedSort}
              onChange={(e) => onSortChange?.(e.target.value)}
              options={[
                { value: '', label: 'الترتيب الافتراضي' },
                ...sortOptions.map(option => ({
                  value: option.value || option,
                  label: option.label || option
                }))
              ]}
              size="md"
            />
          </div>
        )}
        
        {/* زر المسح */}
        {showClearButton && hasActiveFilters && (
          <div className="lg:w-auto">
            <Button
              variant="outline"
              size="md"
              onClick={handleClear}
              icon={X}
              className="w-full lg:w-auto"
            >
              مسح الفلاتر
            </Button>
          </div>
        )}
      </div>
      
      {/* عرض الفلاتر النشطة */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-gray-600 flex items-center">
              <Filter className="w-4 h-4 ml-1" />
              الفلاتر النشطة:
            </span>
            
            {searchValue && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-iraqi-blue text-white">
                البحث: "{searchValue}"
              </span>
            )}
            
            {selectedCategory && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-iraqi-gold text-white">
                الفئة: {categories.find(cat => (cat.value || cat) === selectedCategory)?.label || selectedCategory}
              </span>
            )}
            
            {selectedSort && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-iraqi-green text-white">
                الترتيب: {sortOptions.find(option => (option.value || option) === selectedSort)?.label || selectedSort}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default SearchFilter
