import React, { useState } from 'react'
import { 
  FileText, 
  Users, 
  Heart, 
  Home,
  GraduationCap,
  Briefcase,
  Shield,
  Phone,
  Mail,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  Send
} from 'lucide-react'

const Support = () => {
  const [selectedService, setSelectedService] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    nationalId: '',
    address: '',
    serviceType: '',
    description: '',
    urgency: 'عادي'
  })

  const services = [
    {
      id: 'social',
      title: 'الخدمات الاجتماعية',
      description: 'مساعدات للأسر المحتاجة والحالات الإنسانية',
      icon: Heart,
      color: 'bg-red-500',
      examples: ['مساعدات مالية', 'مساعدات غذائية', 'مساعدات طبية', 'كفالة أيتام']
    },
    {
      id: 'housing',
      title: 'الإسكان والأراضي',
      description: 'خدمات متعلقة بالإسكان وتوزيع الأراضي',
      icon: Home,
      color: 'bg-blue-500',
      examples: ['طلب قطعة أرض', 'مساعدة في الإسكان', 'حل مشاكل السكن', 'تسوية أوضاع الأراضي']
    },
    {
      id: 'education',
      title: 'التعليم والمنح',
      description: 'دعم التعليم والحصول على المنح الدراسية',
      icon: GraduationCap,
      color: 'bg-green-500',
      examples: ['منح دراسية', 'مساعدة في القبول الجامعي', 'دعم الطلاب', 'برامج التدريب']
    },
    {
      id: 'employment',
      title: 'التوظيف والعمل',
      description: 'المساعدة في إيجاد فرص عمل ودعم المشاريع',
      icon: Briefcase,
      color: 'bg-purple-500',
      examples: ['البحث عن عمل', 'دعم المشاريع الصغيرة', 'التدريب المهني', 'القروض الميسرة']
    },
    {
      id: 'legal',
      title: 'الخدمات القانونية',
      description: 'استشارات قانونية ومساعدة في القضايا',
      icon: Shield,
      color: 'bg-yellow-500',
      examples: ['استشارات قانونية', 'مساعدة في القضايا', 'توثيق العقود', 'حل النزاعات']
    },
    {
      id: 'documents',
      title: 'الوثائق والمعاملات',
      description: 'المساعدة في إنجاز المعاملات الحكومية',
      icon: FileText,
      color: 'bg-indigo-500',
      examples: ['استخراج وثائق', 'تسريع المعاملات', 'حل مشاكل الوثائق', 'المتابعة الحكومية']
    }
  ]

  const urgencyLevels = [
    { value: 'عادي', label: 'عادي', color: 'text-gray-600' },
    { value: 'مهم', label: 'مهم', color: 'text-yellow-600' },
    { value: 'عاجل', label: 'عاجل', color: 'text-red-600' }
  ]

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleServiceSelect = (service) => {
    setSelectedService(service)
    setFormData(prev => ({
      ...prev,
      serviceType: service.title
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // هنا سيتم إرسال البيانات إلى الخادم
    console.log('Form submitted:', formData)
    alert('تم إرسال طلبكم بنجاح. سيتم التواصل معكم قريباً.')
  }

  const stats = [
    { label: 'طلبات تم حلها', value: '2,450+', icon: CheckCircle, color: 'text-green-500' },
    { label: 'أسر مستفيدة', value: '1,200+', icon: Users, color: 'text-blue-500' },
    { label: 'خدمات متاحة', value: '6', icon: Heart, color: 'text-red-500' },
    { label: 'متوسط وقت الاستجابة', value: '24 ساعة', icon: Clock, color: 'text-purple-500' }
  ]

  return (
    <div className="min-h-screen py-12">
      <div className="container-custom">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">دعم الناخبين</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            نحن هنا لخدمتكم. اختاروا نوع الخدمة المطلوبة وسنقوم بمساعدتكم في أسرع وقت ممكن
          </p>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="card text-center">
                <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{stat.value}</h3>
                <p className="text-gray-600">{stat.label}</p>
              </div>
            )
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* قائمة الخدمات */}
          <div className="lg:col-span-1">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">أنواع الخدمات</h2>
            <div className="space-y-4">
              {services.map((service) => {
                const Icon = service.icon
                return (
                  <div
                    key={service.id}
                    onClick={() => handleServiceSelect(service)}
                    className={`card cursor-pointer transition-all duration-200 hover:shadow-lg ${
                      selectedService?.id === service.id 
                        ? 'ring-2 ring-iraqi-blue bg-blue-50' 
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-start space-x-4 space-x-reverse">
                      <div className={`w-12 h-12 ${service.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {service.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-3">
                          {service.description}
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {service.examples.slice(0, 2).map((example, index) => (
                            <span
                              key={index}
                              className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs"
                            >
                              {example}
                            </span>
                          ))}
                          {service.examples.length > 2 && (
                            <span className="text-gray-500 text-xs">
                              +{service.examples.length - 2} المزيد
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* نموذج طلب الخدمة */}
          <div className="lg:col-span-2">
            <div className="card">
              <div className="card-header">
                <h2 className="text-2xl font-bold text-gray-900">طلب خدمة</h2>
                <p className="text-gray-600">
                  يرجى ملء النموذج أدناه وسنقوم بالتواصل معكم في أقرب وقت
                </p>
              </div>

              {selectedService && (
                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className={`w-8 h-8 ${selectedService.color} rounded-lg flex items-center justify-center`}>
                      <selectedService.icon className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{selectedService.title}</h3>
                      <p className="text-sm text-gray-600">{selectedService.description}</p>
                    </div>
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* المعلومات الشخصية */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم الكامل *
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                      placeholder="أدخل اسمك الكامل"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهاتف *
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                      placeholder="07XX XXX XXXX"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم الهوية الوطنية
                    </label>
                    <input
                      type="text"
                      name="nationalId"
                      value={formData.nationalId}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                      placeholder="رقم الهوية"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    العنوان *
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                    placeholder="المحافظة، المدينة، الحي"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    درجة الأولوية
                  </label>
                  <select
                    name="urgency"
                    value={formData.urgency}
                    onChange={handleInputChange}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                  >
                    {urgencyLevels.map((level) => (
                      <option key={level.value} value={level.value}>
                        {level.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تفاصيل الطلب *
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    required
                    rows={5}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-iraqi-blue focus:border-transparent"
                    placeholder="اشرح طلبك بالتفصيل..."
                  />
                </div>

                <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                  <p className="text-sm text-gray-500">
                    * الحقول المطلوبة
                  </p>
                  <button
                    type="submit"
                    className="btn-primary inline-flex items-center"
                  >
                    <Send className="w-5 h-5 ml-2" />
                    إرسال الطلب
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        {/* معلومات التواصل */}
        <div className="mt-12 bg-iraqi-blue text-white rounded-lg p-8">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-4">طرق التواصل الأخرى</h2>
            <p className="text-blue-100">
              يمكنكم التواصل معنا مباشرة عبر الطرق التالية
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Phone className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">الهاتف</h3>
              <p className="text-blue-100">+964-XXX-XXXX-XXX</p>
              <p className="text-blue-100 text-sm">متاح 24/7 للحالات العاجلة</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <Mail className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">البريد الإلكتروني</h3>
              <p className="text-blue-100"><EMAIL></p>
              <p className="text-blue-100 text-sm">الرد خلال 24 ساعة</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                <MapPin className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">المكتب</h3>
              <p className="text-blue-100">مجلس النواب العراقي</p>
              <p className="text-blue-100 text-sm">المنطقة الخضراء، بغداد</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Support
