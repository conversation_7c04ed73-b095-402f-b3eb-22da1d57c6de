import React, { forwardRef } from 'react'
import { AlertCircle, Eye, EyeOff } from 'lucide-react'

const Input = forwardRef(({
  type = 'text',
  label,
  placeholder,
  error,
  helperText,
  required = false,
  disabled = false,
  icon: Icon,
  iconPosition = 'right',
  size = 'md',
  variant = 'default',
  className = '',
  containerClassName = '',
  showPasswordToggle = false,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false)
  const [isFocused, setIsFocused] = React.useState(false)
  
  const inputType = type === 'password' && showPassword ? 'text' : type
  
  const baseClasses = 'w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed'
  
  const variants = {
    default: 'border-gray-300 focus:border-iraqi-blue focus:ring-iraqi-blue',
    error: 'border-red-500 focus:border-red-500 focus:ring-red-500',
    success: 'border-green-500 focus:border-green-500 focus:ring-green-500'
  }
  
  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-base',
    lg: 'px-5 py-3 text-lg'
  }
  
  const currentVariant = error ? 'error' : variant
  const classes = `${baseClasses} ${variants[currentVariant]} ${sizes[size]} ${className}`
  
  const hasIcon = Icon || (type === 'password' && showPasswordToggle)
  const paddingClass = hasIcon 
    ? iconPosition === 'right' ? 'pr-10' : 'pl-10'
    : ''
  
  return (
    <div className={`space-y-2 ${containerClassName}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={ref}
          type={inputType}
          placeholder={placeholder}
          disabled={disabled}
          className={`${classes} ${paddingClass}`}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        
        {/* أيقونة عامة */}
        {Icon && (
          <div className={`absolute top-1/2 transform -translate-y-1/2 ${
            iconPosition === 'right' ? 'right-3' : 'left-3'
          }`}>
            <Icon className={`w-5 h-5 ${
              isFocused ? 'text-iraqi-blue' : 'text-gray-400'
            }`} />
          </div>
        )}
        
        {/* زر إظهار/إخفاء كلمة المرور */}
        {type === 'password' && showPasswordToggle && (
          <button
            type="button"
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        )}
        
        {/* أيقونة الخطأ */}
        {error && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
            <AlertCircle className="w-5 h-5 text-red-500" />
          </div>
        )}
      </div>
      
      {/* رسالة الخطأ أو النص المساعد */}
      {error && (
        <p className="text-sm text-red-600 flex items-center">
          <AlertCircle className="w-4 h-4 ml-1" />
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="text-sm text-gray-500">{helperText}</p>
      )}
    </div>
  )
})

Input.displayName = 'Input'

export default Input
