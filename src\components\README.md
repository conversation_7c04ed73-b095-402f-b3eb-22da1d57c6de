# نظام التصميم - موقع النائب العراقي

## نظرة عامة

هذا النظام يحتوي على مجموعة شاملة من المكونات القابلة لإعادة الاستخدام المصممة خصيصاً لموقع النائب العراقي. جميع المكونات تدعم اللغة العربية والاتجاه من اليمين إلى اليسار (RTL) وتتبع الهوية البصرية العراقية.

## الألوان الأساسية

```css
--iraqi-blue: #1e40af    /* الأزرق العراقي */
--iraqi-gold: #f59e0b    /* الذهبي العراقي */
--iraqi-green: #16a34a   /* الأخضر العراقي */
--iraqi-red: #dc2626     /* الأحمر العراقي */
```

## هيكل المكونات

### مكونات UI الأساسية (`/UI`)

#### Button
مكون الأزرار مع دعم متعدد الأشكال والأحجام:

```jsx
import { Button, PrimaryButton } from '../components'

// الاستخدام الأساسي
<Button variant="primary" size="md">
  النص
</Button>

// مع أيقونة
<Button variant="secondary" icon={Plus} iconPosition="right">
  إضافة جديد
</Button>

// حالة التحميل
<Button loading={true}>
  جاري الحفظ...
</Button>
```

**الأشكال المتاحة:**
- `primary` - الأزرار الأساسية
- `secondary` - الأزرار الثانوية
- `success` - أزرار النجاح
- `danger` - أزرار الخطر
- `outline` - أزرار بحدود
- `ghost` - أزرار شفافة
- `link` - أزرار الروابط

#### Card
مكون البطاقات مع دعم التركيب:

```jsx
import { Card } from '../components'

<Card variant="elevated">
  <Card.Header>
    <Card.Title>العنوان</Card.Title>
    <Card.Description>الوصف</Card.Description>
  </Card.Header>
  <Card.Content>
    المحتوى
  </Card.Content>
  <Card.Footer>
    التذييل
  </Card.Footer>
</Card>
```

#### Input
مكون الإدخال مع دعم التحقق والأيقونات:

```jsx
import { Input } from '../components'

<Input
  label="الاسم"
  placeholder="أدخل اسمك"
  icon={User}
  error="هذا الحقل مطلوب"
  required
/>
```

#### Modal
مكون النوافذ المنبثقة:

```jsx
import { Modal } from '../components'

<Modal isOpen={isOpen} onClose={handleClose} size="lg">
  <Modal.Header>
    <Modal.Title>العنوان</Modal.Title>
  </Modal.Header>
  <Modal.Body>
    المحتوى
  </Modal.Body>
  <Modal.Footer>
    <Button onClick={handleClose}>إغلاق</Button>
  </Modal.Footer>
</Modal>
```

#### Alert
مكون التنبيهات:

```jsx
import { Alert } from '../components'

<Alert variant="success" title="نجح!" dismissible>
  تم حفظ البيانات بنجاح
</Alert>
```

#### Loading
مكون التحميل:

```jsx
import { Loading } from '../components'

<Loading variant="spinner" text="جاري التحميل..." />
<Loading variant="dots" size="lg" />
<Loading fullScreen />
```

### المكونات المشتركة (`/Common`)

#### PageHeader
رأس الصفحة مع مسار التنقل:

```jsx
import { PageHeader } from '../components'

<PageHeader
  title="الأخبار"
  subtitle="آخر الأخبار والفعاليات"
  breadcrumbs={[
    { label: 'الأخبار', href: '/news' },
    { label: 'تفاصيل الخبر' }
  ]}
  gradient={true}
/>
```

#### NewsCard
بطاقة الأخبار:

```jsx
import { NewsCard } from '../components'

<NewsCard
  title="عنوان الخبر"
  excerpt="مقتطف من الخبر..."
  category="قوانين"
  date="2024-01-15"
  views={150}
  image="/path/to/image.jpg"
  featured={true}
  onClick={handleClick}
/>
```

#### ProjectCard
بطاقة المشاريع:

```jsx
import { ProjectCard } from '../components'

<ProjectCard
  title="مشروع تطوير البنية التحتية"
  description="وصف المشروع..."
  status="قيد التنفيذ"
  progress={75}
  budget={5000000}
  beneficiaries={1000}
  location="بغداد"
  startDate="2024-01-01"
  endDate="2024-12-31"
/>
```

#### StatsCard
بطاقة الإحصائيات:

```jsx
import { StatsCard } from '../components'

<StatsCard
  title="عدد المشاريع"
  value={25}
  subtitle="مشروع مكتمل"
  icon={FileText}
  color="iraqi-blue"
  trend="up"
  trendValue="+12%"
/>
```

#### SearchFilter
مكون البحث والفلترة:

```jsx
import { SearchFilter } from '../components'

<SearchFilter
  searchValue={search}
  onSearchChange={setSearch}
  categories={['قوانين', 'زيارات', 'اجتماعات']}
  selectedCategory={category}
  onCategoryChange={setCategory}
  sortOptions={[
    { value: 'date', label: 'التاريخ' },
    { value: 'title', label: 'العنوان' }
  ]}
/>
```

### مكونات النماذج (`/Forms`)

#### ContactForm
نموذج التواصل الكامل:

```jsx
import { ContactForm } from '../components'

<ContactForm
  onSubmit={handleSubmit}
  loading={isSubmitting}
/>
```

## الاستيراد

يمكن استيراد جميع المكونات من مكان واحد:

```jsx
import {
  Button,
  Card,
  Input,
  Modal,
  Alert,
  Loading,
  PageHeader,
  NewsCard,
  ProjectCard,
  StatsCard,
  SearchFilter,
  ContactForm
} from '../components'
```

## إرشادات الاستخدام

### 1. الاتساق
- استخدم الألوان العراقية المحددة
- اتبع نمط التسمية العربية
- حافظ على التباعد المتسق

### 2. إمكانية الوصول
- جميع المكونات تدعم لوحة المفاتيح
- تتضمن خصائص ARIA المناسبة
- تدعم قارئات الشاشة

### 3. الاستجابة
- جميع المكونات مصممة للعمل على جميع الأحجام
- تستخدم نهج Mobile-First
- تتكيف مع اتجاه RTL

### 4. الأداء
- المكونات محسنة للأداء
- تستخدم React.memo عند الحاجة
- تدعم التحميل التدريجي

## التطوير المستقبلي

- [ ] إضافة مكونات جديدة (Tabs, Dropdown, Pagination)
- [ ] تحسين الرسوم المتحركة
- [ ] إضافة اختبارات الوحدة
- [ ] توثيق تفاعلي (Storybook)
- [ ] دعم الوضع المظلم
