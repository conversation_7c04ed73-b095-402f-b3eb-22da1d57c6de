import React, { forwardRef } from 'react'
import { ChevronDown, AlertCircle } from 'lucide-react'

const Select = forwardRef(({
  label,
  placeholder = 'اختر خياراً',
  options = [],
  error,
  helperText,
  required = false,
  disabled = false,
  size = 'md',
  variant = 'default',
  className = '',
  containerClassName = '',
  ...props
}, ref) => {
  const baseClasses = 'w-full rounded-lg border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed appearance-none bg-white'
  
  const variants = {
    default: 'border-gray-300 focus:border-iraqi-blue focus:ring-iraqi-blue',
    error: 'border-red-500 focus:border-red-500 focus:ring-red-500',
    success: 'border-green-500 focus:border-green-500 focus:ring-green-500'
  }
  
  const sizes = {
    sm: 'px-3 py-2 text-sm pr-8',
    md: 'px-4 py-2.5 text-base pr-10',
    lg: 'px-5 py-3 text-lg pr-12'
  }
  
  const currentVariant = error ? 'error' : variant
  const classes = `${baseClasses} ${variants[currentVariant]} ${sizes[size]} ${className}`
  
  return (
    <div className={`space-y-2 ${containerClassName}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 mr-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <select
          ref={ref}
          disabled={disabled}
          className={classes}
          {...props}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option, index) => (
            <option 
              key={option.value || index} 
              value={option.value || option}
            >
              {option.label || option}
            </option>
          ))}
        </select>
        
        {/* أيقونة السهم */}
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <ChevronDown className="w-5 h-5 text-gray-400" />
        </div>
        
        {/* أيقونة الخطأ */}
        {error && (
          <div className="absolute left-8 top-1/2 transform -translate-y-1/2">
            <AlertCircle className="w-5 h-5 text-red-500" />
          </div>
        )}
      </div>
      
      {/* رسالة الخطأ أو النص المساعد */}
      {error && (
        <p className="text-sm text-red-600 flex items-center">
          <AlertCircle className="w-4 h-4 ml-1" />
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="text-sm text-gray-500">{helperText}</p>
      )}
    </div>
  )
})

Select.displayName = 'Select'

export default Select
