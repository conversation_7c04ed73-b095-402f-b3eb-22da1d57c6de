import React from 'react'
import { Calendar, Users, DollarSign, FileText, MapPin } from 'lucide-react'
import { Badge } from '../UI'

const ProjectCard = ({
  title,
  description,
  status,
  progress,
  budget,
  beneficiaries,
  location,
  startDate,
  endDate,
  documents = [],
  onClick,
  className = '',
  ...props
}) => {
  const getStatusConfig = (status) => {
    const configs = {
      'مكتمل': { variant: 'success', color: 'bg-green-500' },
      'قيد التنفيذ': { variant: 'primary', color: 'bg-blue-500' },
      'متوقف': { variant: 'danger', color: 'bg-red-500' },
      'مخطط': { variant: 'warning', color: 'bg-yellow-500' }
    }
    return configs[status] || { variant: 'default', color: 'bg-gray-500' }
  }
  
  const formatBudget = (amount) => {
    if (amount >= 1000000000) {
      return (amount / 1000000000).toFixed(1) + ' مليار دينار'
    } else if (amount >= 1000000) {
      return (amount / 1000000).toFixed(1) + ' مليون دينار'
    } else if (amount >= 1000) {
      return (amount / 1000).toFixed(1) + ' ألف دينار'
    }
    return amount?.toLocaleString('ar-IQ') + ' دينار'
  }
  
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('ar-IQ', {
      year: 'numeric',
      month: 'short'
    })
  }
  
  const statusConfig = getStatusConfig(status)
  
  return (
    <div 
      className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 overflow-hidden cursor-pointer group ${className}`}
      onClick={onClick}
      {...props}
    >
      <div className="p-6">
        {/* رأس البطاقة */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-bold text-gray-900 mb-2 group-hover:text-iraqi-blue transition-colors line-clamp-2">
              {title}
            </h3>
            <Badge variant={statusConfig.variant} size="sm">
              {status}
            </Badge>
          </div>
        </div>
        
        {/* الوصف */}
        {description && (
          <p className="text-gray-600 mb-4 line-clamp-3 text-sm leading-relaxed">
            {description}
          </p>
        )}
        
        {/* شريط التقدم */}
        {progress !== undefined && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">نسبة الإنجاز</span>
              <span className="text-sm font-bold text-iraqi-blue">{progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${statusConfig.color}`}
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>
        )}
        
        {/* معلومات المشروع */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4 text-sm">
          {budget && (
            <div className="flex items-center text-gray-600">
              <DollarSign className="w-4 h-4 ml-2 text-iraqi-gold" />
              <span>{formatBudget(budget)}</span>
            </div>
          )}
          
          {beneficiaries && (
            <div className="flex items-center text-gray-600">
              <Users className="w-4 h-4 ml-2 text-iraqi-green" />
              <span>{beneficiaries.toLocaleString('ar-IQ')} مستفيد</span>
            </div>
          )}
          
          {location && (
            <div className="flex items-center text-gray-600">
              <MapPin className="w-4 h-4 ml-2 text-iraqi-red" />
              <span>{location}</span>
            </div>
          )}
          
          {documents.length > 0 && (
            <div className="flex items-center text-gray-600">
              <FileText className="w-4 h-4 ml-2 text-iraqi-blue" />
              <span>{documents.length} وثيقة</span>
            </div>
          )}
        </div>
        
        {/* التواريخ */}
        {(startDate || endDate) && (
          <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-200">
            {startDate && (
              <div className="flex items-center">
                <Calendar className="w-4 h-4 ml-1" />
                <span>بداية: {formatDate(startDate)}</span>
              </div>
            )}
            
            {endDate && (
              <div className="flex items-center">
                <Calendar className="w-4 h-4 ml-1" />
                <span>نهاية: {formatDate(endDate)}</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default ProjectCard
